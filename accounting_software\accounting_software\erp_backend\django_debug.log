INFO 2025-07-09 17:12:19,909 autoreload 11336 15312 Watching for file changes with StatReloader
INFO 2025-07-09 17:12:55,869 basehttp 11336 11024 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,873 basehttp 11336 11024 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,873 basehttp 11336 16356 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,884 basehttp 11336 16356 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,884 basehttp 11336 11024 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,889 basehttp 11336 15432 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,903 basehttp 11336 15432 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,903 basehttp 11336 11024 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,903 basehttp 11336 16356 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,907 basehttp 11336 13816 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,923 basehttp 11336 15432 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,923 basehttp 11336 16356 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,923 basehttp 11336 15900 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:55,986 basehttp 11336 17320 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-09 17:12:56,016 basehttp 11336 15900 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-09 17:12:56,064 basehttp 11336 13816 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-09 17:12:56,073 basehttp 11336 16356 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-09 17:12:56,082 basehttp 11336 17320 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-09 17:12:56,234 basehttp 11336 13816 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-09 17:12:56,241 basehttp 11336 16356 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-09 17:12:56,247 basehttp 11336 15900 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-09 17:12:56,265 basehttp 11336 17320 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-09 17:12:56,330 basehttp 11336 11024 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16067
INFO 2025-07-09 17:12:56,353 basehttp 11336 13816 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-09 17:12:56,444 basehttp 11336 15432 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-09 17:12:56,505 basehttp 11336 15900 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-09 17:12:56,579 basehttp 11336 13816 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-09 17:12:56,637 basehttp 11336 13816 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-09 17:12:56,713 basehttp 11336 17320 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16067
INFO 2025-07-09 17:12:56,737 basehttp 11336 11024 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-09 17:13:02,294 basehttp 11336 16232 "GET /admin/purchase/purchaseorder/ HTTP/1.1" 302 0
INFO 2025-07-09 17:13:02,508 basehttp 11336 16232 "GET /admin/login/?next=/admin/purchase/purchaseorder/ HTTP/1.1" 200 4227
WARNING 2025-07-09 17:13:03,024 log 11336 16232 Not Found: /favicon.ico
WARNING 2025-07-09 17:13:03,025 basehttp 11336 16232 "GET /favicon.ico HTTP/1.1" 404 3566
INFO 2025-07-09 17:13:04,941 basehttp 11336 16232 "POST /admin/login/?next=/admin/purchase/purchaseorder/ HTTP/1.1" 302 0
INFO 2025-07-09 17:13:05,291 basehttp 11336 16232 "GET /admin/purchase/purchaseorder/ HTTP/1.1" 200 51602
INFO 2025-07-09 17:13:05,336 basehttp 11336 16232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-09 17:16:55,914 basehttp 11336 16232 "GET /admin/inventory/inventory/ HTTP/1.1" 200 47301
INFO 2025-07-09 17:16:55,944 basehttp 11336 16232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-09 17:17:13,217 basehttp 11336 16232 "GET /admin/inventory/vendorinvoice/ HTTP/1.1" 200 30652
INFO 2025-07-09 17:17:13,245 basehttp 11336 16232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-09 17:17:19,367 basehttp 11336 16232 "GET /admin/inventory/warehouselocation/ HTTP/1.1" 200 37243
INFO 2025-07-09 17:17:19,403 basehttp 11336 16232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-09 17:17:40,989 basehttp 11336 16232 "GET /admin/purchase/vendorpayment/ HTTP/1.1" 200 30707
INFO 2025-07-09 17:17:41,024 basehttp 11336 16232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-09 17:17:48,073 basehttp 11336 16232 "GET /admin/purchase/vendorbill/ HTTP/1.1" 200 94607
INFO 2025-07-09 17:17:48,101 basehttp 11336 16232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-09 20:38:46,353 basehttp 11336 18392 "GET /admin/purchase/vendorpayment/ HTTP/1.1" 302 0
INFO 2025-07-09 20:38:46,417 basehttp 11336 18392 "GET /admin/login/?next=/admin/purchase/vendorpayment/ HTTP/1.1" 200 4227
INFO 2025-07-09 20:38:48,894 basehttp 11336 18392 "POST /admin/login/?next=/admin/purchase/vendorpayment/ HTTP/1.1" 302 0
INFO 2025-07-09 20:38:48,974 basehttp 11336 18392 "GET /admin/purchase/vendorpayment/ HTTP/1.1" 200 30707
INFO 2025-07-09 20:38:49,008 basehttp 11336 18392 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-09 20:42:51,911 autoreload 11336 15312 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 02:02:25,297 autoreload 6252 18452 Watching for file changes with StatReloader
INFO 2025-07-10 02:02:31,101 basehttp 6252 16564 "GET /admin/purchase/vendorpayment/ HTTP/1.1" 302 0
INFO 2025-07-10 02:02:31,436 basehttp 6252 16564 "GET /admin/login/?next=/admin/purchase/vendorpayment/ HTTP/1.1" 200 4227
INFO 2025-07-10 02:02:34,211 basehttp 6252 16564 "POST /admin/login/?next=/admin/purchase/vendorpayment/ HTTP/1.1" 302 0
WARNING 2025-07-10 02:02:34,564 log 6252 16564 Not Found: /admin/purchase/vendorpayment/
WARNING 2025-07-10 02:02:34,566 basehttp 6252 16564 "GET /admin/purchase/vendorpayment/ HTTP/1.1" 404 14587
INFO 2025-07-10 02:02:41,721 basehttp 6252 16564 "GET /admin/purchase HTTP/1.1" 301 0
INFO 2025-07-10 02:02:41,917 basehttp 6252 16564 "GET /admin/purchase/ HTTP/1.1" 200 6988
INFO 2025-07-10 02:02:51,569 basehttp 6252 16564 "GET /admin HTTP/1.1" 301 0
INFO 2025-07-10 02:02:51,687 basehttp 6252 17464 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 16:12:29,658 autoreload 12116 14384 Watching for file changes with StatReloader
INFO 2025-07-10 16:13:02,945 basehttp 12116 12952 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,957 basehttp 12116 4236 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,958 basehttp 12116 12952 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,961 basehttp 12116 12692 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,969 basehttp 12116 4236 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,971 basehttp 12116 12952 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,981 basehttp 12116 12692 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,994 basehttp 12116 14052 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:02,994 basehttp 12116 4236 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:03,008 basehttp 12116 8820 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:03,016 basehttp 12116 12952 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:03,020 basehttp 12116 12692 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:03,023 basehttp 12116 14052 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:03,033 basehttp 12116 14120 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 16:13:03,143 basehttp 12116 12952 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 16:13:03,262 basehttp 12116 8820 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 16:13:03,299 basehttp 12116 14120 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:13:03,344 basehttp 12116 4236 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16067
INFO 2025-07-10 16:13:03,381 basehttp 12116 14052 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 16:13:03,435 basehttp 12116 12692 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 16:13:03,479 basehttp 12116 8820 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 16:13:03,502 basehttp 12116 12952 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-10 16:13:03,551 basehttp 12116 12692 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 16:13:03,596 basehttp 12116 14120 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 16:13:03,657 basehttp 12116 4236 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:13:03,681 basehttp 12116 14120 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:13:03,722 basehttp 12116 12952 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-10 16:13:03,798 basehttp 12116 14052 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16067
INFO 2025-07-10 16:13:03,824 basehttp 12116 8820 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 16:13:04,639 basehttp 12116 8820 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:15:39,326 basehttp 12116 15020 "GET /admin/purchase/vendorcredit/ HTTP/1.1" 302 0
INFO 2025-07-10 16:15:39,666 basehttp 12116 15020 "GET /admin/login/?next=/admin/purchase/vendorcredit/ HTTP/1.1" 200 4225
WARNING 2025-07-10 16:15:40,257 log 12116 15020 Not Found: /favicon.ico
WARNING 2025-07-10 16:15:40,257 basehttp 12116 15020 "GET /favicon.ico HTTP/1.1" 404 3566
INFO 2025-07-10 16:15:41,915 basehttp 12116 15020 "POST /admin/login/?next=/admin/purchase/vendorcredit/ HTTP/1.1" 302 0
WARNING 2025-07-10 16:15:41,955 log 12116 15020 Not Found: /admin/purchase/vendorcredit/
WARNING 2025-07-10 16:15:41,956 basehttp 12116 15020 "GET /admin/purchase/vendorcredit/ HTTP/1.1" 404 14584
INFO 2025-07-10 16:15:50,340 basehttp 12116 15020 "GET /admin/purchase HTTP/1.1" 301 0
INFO 2025-07-10 16:15:50,447 basehttp 12116 15020 "GET /admin/purchase/ HTTP/1.1" 200 6988
INFO 2025-07-10 16:15:54,348 basehttp 12116 15020 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 16:15:57,027 basehttp 12116 15020 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 16:16:00,906 basehttp 12116 8820 "OPTIONS /api/purchase/vendor-bills/? HTTP/1.1" 200 0
INFO 2025-07-10 16:16:00,907 basehttp 12116 14052 "OPTIONS /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:00,908 basehttp 12116 12952 "OPTIONS /api/purchase/vendor-bills/? HTTP/1.1" 200 0
INFO 2025-07-10 16:16:00,908 basehttp 12116 14120 "OPTIONS /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:00,943 basehttp 12116 14052 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:00,979 basehttp 12116 14052 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:00,995 basehttp 12116 14120 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:01,025 basehttp 12116 14120 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:03,112 basehttp 12116 14120 "OPTIONS /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:03,112 basehttp 12116 14052 "OPTIONS /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:03,158 basehttp 12116 14052 "GET /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 5383
INFO 2025-07-10 16:16:03,209 basehttp 12116 14052 "GET /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 5383
INFO 2025-07-10 16:16:06,516 basehttp 12116 14120 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:06,533 basehttp 12116 14052 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:06,546 basehttp 12116 14120 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:06,568 basehttp 12116 14052 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:07,843 basehttp 12116 14120 "OPTIONS /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:07,843 basehttp 12116 14052 "OPTIONS /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:07,900 basehttp 12116 14120 "GET /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 2
INFO 2025-07-10 16:16:07,946 basehttp 12116 14120 "GET /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 2
INFO 2025-07-10 16:16:09,321 basehttp 12116 14052 "OPTIONS /api/purchase/purchase-orders/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:09,322 basehttp 12116 8820 "OPTIONS /api/purchase/purchase-orders/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:09,322 basehttp 12116 12952 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:09,322 basehttp 12116 14120 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:09,339 basehttp 12116 8820 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:16:09,355 basehttp 12116 8820 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:16:09,355 basehttp 12116 14120 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 16:16:09,398 basehttp 12116 8820 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 16:16:14,404 basehttp 12116 14120 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:14,417 basehttp 12116 8820 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:14,436 basehttp 12116 14120 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:14,460 basehttp 12116 8820 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:16,260 basehttp 12116 8820 "OPTIONS /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:16,260 basehttp 12116 14120 "OPTIONS /api/sales/products/?page_size=100 HTTP/1.1" 200 0
INFO 2025-07-10 16:16:16,260 basehttp 12116 12952 "OPTIONS /api/sales-tax/?tax_type=input HTTP/1.1" 200 0
INFO 2025-07-10 16:16:16,261 basehttp 12116 4236 "OPTIONS /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 0
INFO 2025-07-10 16:16:16,261 basehttp 12116 12692 "OPTIONS /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 0
INFO 2025-07-10 16:16:16,275 basehttp 12116 14052 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:16:16,331 basehttp 12116 14052 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:16:16,339 basehttp 12116 14120 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:16:16,384 basehttp 12116 12692 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 16:16:16,438 basehttp 12116 14120 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:16:16,457 basehttp 12116 4236 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:16:16,483 basehttp 12116 14052 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 16:16:16,527 basehttp 12116 8820 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:16:16,628 basehttp 12116 8820 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:16:16,642 basehttp 12116 14120 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:16:16,661 basehttp 12116 14052 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 16:16:16,730 basehttp 12116 14120 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:16:16,760 basehttp 12116 14052 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:16:16,822 basehttp 12116 14120 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:16:16,872 basehttp 12116 14120 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 16:16:21,221 basehttp 12116 14052 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:21,232 basehttp 12116 14120 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:21,247 basehttp 12116 14052 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:21,267 basehttp 12116 14120 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:33,491 basehttp 12116 12692 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:16:33,515 basehttp 12116 14052 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 16:16:33,516 basehttp 12116 12952 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 16:16:33,525 basehttp 12116 4236 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 16:16:33,795 basehttp 12116 12692 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-10 16:16:33,829 basehttp 12116 14120 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16067
INFO 2025-07-10 16:16:33,870 basehttp 12116 14052 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 16:16:33,887 basehttp 12116 8820 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 16:16:33,891 basehttp 12116 12952 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:16:33,958 basehttp 12116 4236 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 16:16:34,052 basehttp 12116 12692 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 16:16:34,119 basehttp 12116 14120 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-10 16:16:34,129 basehttp 12116 14052 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:16:34,333 basehttp 12116 14120 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 16:16:34,362 basehttp 12116 12692 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:34,385 basehttp 12116 8820 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 16:16:34,402 basehttp 12116 4236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:34,437 basehttp 12116 12692 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:16:34,464 basehttp 12116 12952 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16067
INFO 2025-07-10 16:16:34,471 basehttp 12116 4236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:16:41,208 basehttp 12116 12952 "OPTIONS /api/gl/financial-statements/?statement_type=all&period_type=fiscal_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 0
INFO 2025-07-10 16:16:41,209 basehttp 12116 12692 "OPTIONS /api/gl/financial-statements/?statement_type=all&period_type=fiscal_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 0
INFO 2025-07-10 16:16:41,214 basehttp 12116 4236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:16:41,250 basehttp 12116 4236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:16:41,405 basehttp 12116 12692 "GET /api/gl/financial-statements/?statement_type=all&period_type=fiscal_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 21154
INFO 2025-07-10 16:16:41,595 basehttp 12116 4236 "GET /api/gl/financial-statements/?statement_type=all&period_type=fiscal_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 21154
INFO 2025-07-10 16:16:45,002 basehttp 12116 4236 "OPTIONS /api/gl/financial-statements/?statement_type=all&period_type=calendar_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 0
INFO 2025-07-10 16:16:45,142 basehttp 12116 4236 "GET /api/gl/financial-statements/?statement_type=all&period_type=calendar_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 21044
INFO 2025-07-10 16:16:54,132 basehttp 12116 4236 "GET /api/gl/financial-statements/?statement_type=all&period_type=fiscal_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 21154
INFO 2025-07-10 16:17:31,990 basehttp 12116 15020 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 16:21:00,085 basehttp 12116 4236 "GET /api/gl/financial-statements/?statement_type=all&period_type=calendar_year&year=2025&currency=functional&include_comparatives=true HTTP/1.1" 200 21044
INFO 2025-07-10 16:26:36,955 basehttp 12116 15660 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:26:36,978 basehttp 12116 8100 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:26:36,995 basehttp 12116 15660 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:26:37,016 basehttp 12116 8100 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2337
INFO 2025-07-10 16:30:34,329 autoreload 12116 14384 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 16:30:36,844 autoreload 6648 2504 Watching for file changes with StatReloader
INFO 2025-07-10 16:30:49,558 autoreload 6648 2504 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 16:30:52,057 autoreload 12860 11688 Watching for file changes with StatReloader
INFO 2025-07-10 16:31:28,238 autoreload 12860 11688 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\views.py changed, reloading.
INFO 2025-07-10 16:31:31,213 autoreload 2484 8924 Watching for file changes with StatReloader
INFO 2025-07-10 16:32:31,362 autoreload 2484 8924 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\views.py changed, reloading.
INFO 2025-07-10 16:32:34,516 autoreload 10020 15032 Watching for file changes with StatReloader
INFO 2025-07-10 16:32:58,035 autoreload 10020 15032 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\serializers.py changed, reloading.
INFO 2025-07-10 16:32:59,767 autoreload 6872 6064 Watching for file changes with StatReloader
INFO 2025-07-10 16:33:22,857 basehttp 6872 9316 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
WARNING 2025-07-10 16:33:23,168 log 6872 9408 Bad Request: /api/purchase/vendor-bills/
WARNING 2025-07-10 16:33:23,184 basehttp 6872 9408 "GET /api/purchase/vendor-bills/? HTTP/1.1" 400 59
INFO 2025-07-10 16:33:37,362 basehttp 6872 9316 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
WARNING 2025-07-10 16:33:37,378 log 6872 9408 Bad Request: /api/purchase/vendor-bills/
WARNING 2025-07-10 16:33:37,379 basehttp 6872 9408 "GET /api/purchase/vendor-bills/? HTTP/1.1" 400 59
INFO 2025-07-10 16:33:58,372 basehttp 6872 9316 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
WARNING 2025-07-10 16:33:58,385 log 6872 9408 Bad Request: /api/purchase/vendor-bills/
WARNING 2025-07-10 16:33:58,386 basehttp 6872 9408 "GET /api/purchase/vendor-bills/? HTTP/1.1" 400 59
INFO 2025-07-10 16:37:15,400 autoreload 16028 1080 Watching for file changes with StatReloader
INFO 2025-07-10 16:37:46,965 basehttp 6872 14700 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 960
INFO 2025-07-10 16:52:00,539 basehttp 6872 17236 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
WARNING 2025-07-10 16:52:00,805 log 6872 17236 Unauthorized: /api/sales/products/
INFO 2025-07-10 16:52:00,808 basehttp 6872 10792 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
WARNING 2025-07-10 16:52:00,885 basehttp 6872 17236 "GET /api/sales/products/ HTTP/1.1" 401 58
INFO 2025-07-10 16:52:01,039 basehttp 6872 14572 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
WARNING 2025-07-10 16:52:01,042 log 6872 10792 Unauthorized: /api/purchase/vendors/
WARNING 2025-07-10 16:52:01,089 basehttp 6872 10792 "GET /api/purchase/vendors/ HTTP/1.1" 401 58
WARNING 2025-07-10 16:52:01,085 log 6872 14572 Unauthorized: /api/sales/products/
WARNING 2025-07-10 16:52:01,092 basehttp 6872 14572 "GET /api/sales/products/ HTTP/1.1" 401 58
INFO 2025-07-10 16:52:01,101 basehttp 6872 10152 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
WARNING 2025-07-10 16:52:01,166 log 6872 17236 Unauthorized: /api/sales/categories/
INFO 2025-07-10 16:52:01,176 basehttp 6872 10656 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
WARNING 2025-07-10 16:52:01,185 log 6872 10792 Unauthorized: /api/purchase/vendors/
WARNING 2025-07-10 16:52:01,189 log 6872 14572 Unauthorized: /api/sales/payment-terms/
WARNING 2025-07-10 16:52:01,192 basehttp 6872 17236 "GET /api/sales/categories/ HTTP/1.1" 401 27
WARNING 2025-07-10 16:52:01,198 basehttp 6872 10792 "GET /api/purchase/vendors/ HTTP/1.1" 401 58
WARNING 2025-07-10 16:52:01,251 basehttp 6872 14572 "GET /api/sales/payment-terms/ HTTP/1.1" 401 27
INFO 2025-07-10 16:52:01,251 basehttp 6872 9408 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
WARNING 2025-07-10 16:52:01,315 log 6872 14572 Unauthorized: /api/sales/payment-terms/
WARNING 2025-07-10 16:52:01,316 log 6872 17236 Unauthorized: /api/sales/categories/
WARNING 2025-07-10 16:52:01,324 basehttp 6872 14572 "GET /api/sales/payment-terms/ HTTP/1.1" 401 27
WARNING 2025-07-10 16:52:01,327 basehttp 6872 17236 "GET /api/sales/categories/ HTTP/1.1" 401 27
INFO 2025-07-10 16:52:02,354 basehttp 6872 17236 "OPTIONS /api-token-auth/ HTTP/1.1" 200 0
WARNING 2025-07-10 16:52:03,098 log 6872 17236 Bad Request: /api-token-auth/
WARNING 2025-07-10 16:52:03,099 basehttp 6872 17236 "POST /api-token-auth/ HTTP/1.1" 400 68
INFO 2025-07-10 16:52:05,499 basehttp 6872 9688 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 960
INFO 2025-07-10 16:52:07,139 basehttp 6872 17136 "GET /admin/ HTTP/1.1" 200 38078
WARNING 2025-07-10 16:52:10,541 log 6872 17236 Bad Request: /api-token-auth/
WARNING 2025-07-10 16:52:10,542 basehttp 6872 17236 "POST /api-token-auth/ HTTP/1.1" 400 68
WARNING 2025-07-10 16:52:13,130 log 6872 17236 Bad Request: /api-token-auth/
WARNING 2025-07-10 16:52:13,133 basehttp 6872 17236 "POST /api-token-auth/ HTTP/1.1" 400 68
INFO 2025-07-10 16:52:15,343 basehttp 6872 9688 "POST /api/purchase/vendor-bills/create_credit_from_return_note/ HTTP/1.1" 201 173
WARNING 2025-07-10 16:52:51,825 log 6872 17236 Unauthorized: /api/purchase/vendors/
WARNING 2025-07-10 16:52:51,835 log 6872 10792 Unauthorized: /api/sales/products/
WARNING 2025-07-10 16:52:51,843 log 6872 9408 Unauthorized: /api/sales/payment-terms/
WARNING 2025-07-10 16:52:51,844 log 6872 14572 Unauthorized: /api/sales/categories/
WARNING 2025-07-10 16:52:51,917 basehttp 6872 17236 "GET /api/purchase/vendors/ HTTP/1.1" 401 58
WARNING 2025-07-10 16:52:51,920 basehttp 6872 10792 "GET /api/sales/products/ HTTP/1.1" 401 58
WARNING 2025-07-10 16:52:51,982 basehttp 6872 9408 "GET /api/sales/payment-terms/ HTTP/1.1" 401 27
WARNING 2025-07-10 16:52:52,044 basehttp 6872 14572 "GET /api/sales/categories/ HTTP/1.1" 401 27
WARNING 2025-07-10 16:52:52,059 log 6872 17236 Unauthorized: /api/sales/products/
WARNING 2025-07-10 16:52:52,131 basehttp 6872 17236 "GET /api/sales/products/ HTTP/1.1" 401 58
WARNING 2025-07-10 16:52:52,074 log 6872 10656 Unauthorized: /api/sales/categories/
WARNING 2025-07-10 16:52:52,094 log 6872 9408 Unauthorized: /api/sales/payment-terms/
WARNING 2025-07-10 16:52:52,074 log 6872 10792 Unauthorized: /api/purchase/vendors/
WARNING 2025-07-10 16:52:52,145 basehttp 6872 10656 "GET /api/sales/categories/ HTTP/1.1" 401 27
WARNING 2025-07-10 16:52:52,147 basehttp 6872 9408 "GET /api/sales/payment-terms/ HTTP/1.1" 401 27
WARNING 2025-07-10 16:52:52,149 basehttp 6872 10792 "GET /api/purchase/vendors/ HTTP/1.1" 401 58
WARNING 2025-07-10 16:52:58,600 log 6872 10792 Bad Request: /api-token-auth/
WARNING 2025-07-10 16:52:58,602 basehttp 6872 10792 "POST /api-token-auth/ HTTP/1.1" 400 68
INFO 2025-07-10 16:53:16,856 basehttp 6872 17136 "GET /admin/ HTTP/1.1" 200 38078
WARNING 2025-07-10 16:53:20,295 log 6872 10792 Bad Request: /api-token-auth/
WARNING 2025-07-10 16:53:20,296 basehttp 6872 10792 "POST /api-token-auth/ HTTP/1.1" 400 68
WARNING 2025-07-10 16:54:06,924 log 6872 13808 Not Found: /api/auth/login/
WARNING 2025-07-10 16:54:06,924 basehttp 6872 13808 "POST /api/auth/login/ HTTP/1.1" 404 5765
WARNING 2025-07-10 16:54:15,252 log 6872 13808 Bad Request: /api-token-auth/
WARNING 2025-07-10 16:54:15,253 basehttp 6872 13808 "POST /api-token-auth/ HTTP/1.1" 400 68
WARNING 2025-07-10 16:55:25,538 log 6872 10792 Bad Request: /api-token-auth/
WARNING 2025-07-10 16:55:25,597 basehttp 6872 10792 "POST /api-token-auth/ HTTP/1.1" 400 68
INFO 2025-07-10 16:55:44,693 basehttp 6872 13808 "POST /api-token-auth/ HTTP/1.1" 200 52
INFO 2025-07-10 16:57:30,943 basehttp 6872 10792 "POST /api-token-auth/ HTTP/1.1" 200 52
INFO 2025-07-10 16:57:30,947 basehttp 6872 10792 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:30,958 basehttp 6872 10792 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 16:57:41,145 basehttp 6872 10656 "OPTIONS /api/purchase/vendor-bills/? HTTP/1.1" 200 0
INFO 2025-07-10 16:57:41,145 basehttp 6872 10792 "OPTIONS /api/purchase/vendor-bills/? HTTP/1.1" 200 0
INFO 2025-07-10 16:57:41,145 basehttp 6872 17236 "OPTIONS /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:41,145 basehttp 6872 9408 "OPTIONS /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:41,208 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2353
INFO 2025-07-10 16:57:41,278 basehttp 6872 9408 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:57:41,286 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2353
INFO 2025-07-10 16:57:41,342 basehttp 6872 9408 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:57:43,041 basehttp 6872 9408 "OPTIONS /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:43,041 basehttp 6872 17236 "OPTIONS /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:43,076 basehttp 6872 17236 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 404
INFO 2025-07-10 16:57:43,101 basehttp 6872 17236 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 404
INFO 2025-07-10 16:57:48,325 basehttp 6872 17236 "OPTIONS /api/purchase/vendor-bills/create_credit_from_return_note/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:48,444 basehttp 6872 17236 "POST /api/purchase/vendor-bills/create_credit_from_return_note/ HTTP/1.1" 201 173
INFO 2025-07-10 16:57:50,634 basehttp 6872 9408 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:57:50,660 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2368
INFO 2025-07-10 16:57:50,666 basehttp 6872 9408 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:57:50,720 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2368
INFO 2025-07-10 16:57:54,819 basehttp 6872 9408 "OPTIONS /api/sales/products/?page_size=100 HTTP/1.1" 200 0
INFO 2025-07-10 16:57:54,819 basehttp 6872 17236 "OPTIONS /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:54,820 basehttp 6872 10656 "OPTIONS /api/sales-tax/?tax_type=input HTTP/1.1" 200 0
INFO 2025-07-10 16:57:54,820 basehttp 6872 10792 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:54,824 basehttp 6872 17236 "OPTIONS /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 0
INFO 2025-07-10 16:57:54,870 basehttp 6872 10792 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:57:54,925 basehttp 6872 9408 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:57:54,963 basehttp 6872 9408 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 16:57:54,972 basehttp 6872 9408 "OPTIONS /api/purchase/vendor-bills/107/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:54,977 basehttp 6872 9408 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:55,012 basehttp 6872 10792 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 16:57:55,013 basehttp 6872 9408 "OPTIONS /api/purchase/vendor-bills/107/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:55,047 basehttp 6872 9408 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 16:57:55,051 basehttp 6872 10656 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:57:55,102 basehttp 6872 9408 "GET /api/purchase/vendor-bills/107/ HTTP/1.1" 200 684
INFO 2025-07-10 16:57:55,123 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:57:55,191 basehttp 6872 10792 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:57:55,236 basehttp 6872 10656 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-10 16:57:55,250 basehttp 6872 17440 "GET /api/purchase/vendor-bills/107/ HTTP/1.1" 200 684
INFO 2025-07-10 16:57:55,303 basehttp 6872 10792 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:57:55,352 basehttp 6872 9408 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 16:57:55,381 basehttp 6872 17236 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:57:55,381 basehttp 6872 10656 "GET /api/sales/products/ HTTP/1.1" 200 7921
INFO 2025-07-10 16:57:55,451 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 16:57:55,535 basehttp 6872 10656 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:57:55,544 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 16:57:55,604 basehttp 6872 10656 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:57:55,684 basehttp 6872 10656 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:57:55,749 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:57:55,830 basehttp 6872 18284 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:57:55,879 basehttp 6872 10656 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:57:55,899 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 16:57:55,979 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 16:57:56,121 basehttp 6872 18284 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 16:57:56,242 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 16:57:56,242 basehttp 6872 10656 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16067
INFO 2025-07-10 16:58:13,569 basehttp 6872 10656 "PATCH /api/purchase/vendor-bills/107/ HTTP/1.1" 200 689
INFO 2025-07-10 16:58:15,221 basehttp 6872 17236 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:58:15,252 basehttp 6872 17236 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 16:58:15,252 basehttp 6872 10656 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2369
INFO 2025-07-10 16:58:15,291 basehttp 6872 10656 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2369
INFO 2025-07-10 17:01:27,524 basehttp 6872 10656 "OPTIONS /api/gl/journal-entries/ HTTP/1.1" 200 0
INFO 2025-07-10 17:01:27,530 basehttp 6872 17236 "OPTIONS /api/gl/journal-entries/ HTTP/1.1" 200 0
INFO 2025-07-10 17:01:27,672 basehttp 6872 17236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:01:27,724 basehttp 6872 10656 "GET /api/gl/journal-entries/ HTTP/1.1" 200 22239
INFO 2025-07-10 17:01:27,731 basehttp 6872 17236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:01:27,745 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 17:01:27,828 basehttp 6872 10656 "GET /api/gl/journal-entries/ HTTP/1.1" 200 22239
INFO 2025-07-10 17:01:27,861 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:01:54,556 basehttp 6872 10656 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:01:54,575 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2369
INFO 2025-07-10 17:01:54,600 basehttp 6872 10656 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:01:54,619 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2369
INFO 2025-07-10 17:02:13,584 basehttp 6872 17236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:13,600 basehttp 6872 17236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:13,639 basehttp 6872 10656 "GET /api/gl/journal-entries/ HTTP/1.1" 200 22239
INFO 2025-07-10 17:02:13,662 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:02:13,728 basehttp 6872 10656 "GET /api/gl/journal-entries/ HTTP/1.1" 200 22239
INFO 2025-07-10 17:02:13,756 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:02:32,447 basehttp 6872 10656 "OPTIONS /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 0
INFO 2025-07-10 17:02:32,448 basehttp 6872 18284 "OPTIONS /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 0
INFO 2025-07-10 17:02:32,456 basehttp 6872 17236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:32,471 basehttp 6872 17236 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:32,534 basehttp 6872 18284 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 2602
INFO 2025-07-10 17:02:32,660 basehttp 6872 18284 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 2602
INFO 2025-07-10 17:02:34,495 basehttp 6872 10656 "OPTIONS /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 0
INFO 2025-07-10 17:02:34,497 basehttp 6872 17236 "OPTIONS /api/inventory/grn-returns/returnable_grns/ HTTP/1.1" 200 0
INFO 2025-07-10 17:02:34,499 basehttp 6872 9408 "OPTIONS /api/inventory/grn-returns/returnable_grns/ HTTP/1.1" 200 0
INFO 2025-07-10 17:02:34,499 basehttp 6872 10792 "OPTIONS /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 0
INFO 2025-07-10 17:02:34,512 basehttp 6872 18284 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:34,578 basehttp 6872 18284 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:34,632 basehttp 6872 10792 "GET /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 4579
INFO 2025-07-10 17:02:34,706 basehttp 6872 10792 "GET /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 4579
INFO 2025-07-10 17:02:34,799 basehttp 6872 17236 "GET /api/inventory/grn-returns/returnable_grns/ HTTP/1.1" 200 9485
INFO 2025-07-10 17:02:34,990 basehttp 6872 10792 "GET /api/inventory/grn-returns/returnable_grns/ HTTP/1.1" 200 9485
INFO 2025-07-10 17:02:56,248 basehttp 6872 10792 "OPTIONS /api/inventory/grn-returns/ HTTP/1.1" 200 0
INFO 2025-07-10 17:02:56,359 basehttp 6872 10792 "POST /api/inventory/grn-returns/ HTTP/1.1" 201 477
INFO 2025-07-10 17:02:56,589 basehttp 6872 10792 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:56,681 basehttp 6872 10792 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:02:56,686 basehttp 6872 17236 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3593
INFO 2025-07-10 17:02:56,818 basehttp 6872 17236 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3593
INFO 2025-07-10 17:03:01,404 basehttp 6872 17236 "OPTIONS /api/inventory/grn-returns/3/approve/ HTTP/1.1" 200 0
INFO 2025-07-10 17:03:01,432 basehttp 6872 17236 "POST /api/inventory/grn-returns/3/approve/ HTTP/1.1" 200 47
INFO 2025-07-10 17:03:01,680 basehttp 6872 17236 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3640
INFO 2025-07-10 17:03:05,253 basehttp 6872 17236 "OPTIONS /api/inventory/grn-returns/3/mark_returned/ HTTP/1.1" 200 0
INFO 2025-07-10 17:03:05,284 basehttp 6872 17236 "POST /api/inventory/grn-returns/3/mark_returned/ HTTP/1.1" 200 57
INFO 2025-07-10 17:03:05,378 basehttp 6872 17236 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3640
INFO 2025-07-10 17:03:09,468 basehttp 6872 17236 "OPTIONS /api/inventory/grn-returns/3/post_to_inventory/ HTTP/1.1" 200 0
INFO 2025-07-10 17:03:09,613 basehttp 6872 17236 "POST /api/inventory/grn-returns/3/post_to_inventory/ HTTP/1.1" 200 58
INFO 2025-07-10 17:03:09,748 basehttp 6872 17236 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3680
INFO 2025-07-10 17:03:14,959 basehttp 6872 10792 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:03:14,969 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2369
INFO 2025-07-10 17:03:14,986 basehttp 6872 10792 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:03:15,005 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2369
INFO 2025-07-10 17:03:16,503 basehttp 6872 17236 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 395
INFO 2025-07-10 17:03:16,534 basehttp 6872 17236 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 395
INFO 2025-07-10 17:03:36,094 basehttp 6872 17236 "POST /api/purchase/vendor-bills/create_credit_from_return_note/ HTTP/1.1" 201 173
INFO 2025-07-10 17:03:38,278 basehttp 6872 10792 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:03:38,306 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2378
INFO 2025-07-10 17:03:38,308 basehttp 6872 10792 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:03:38,347 basehttp 6872 17236 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2378
INFO 2025-07-10 17:03:41,595 basehttp 6872 9408 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:03:41,630 basehttp 6872 9408 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:03:41,659 basehttp 6872 9408 "OPTIONS /api/purchase/vendor-bills/108/ HTTP/1.1" 200 0
INFO 2025-07-10 17:03:41,664 basehttp 6872 18284 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:03:41,676 basehttp 6872 10656 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 17:03:41,693 basehttp 6872 9408 "OPTIONS /api/purchase/vendor-bills/108/ HTTP/1.1" 200 0
INFO 2025-07-10 17:03:41,747 basehttp 6872 9408 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 17:03:41,776 basehttp 6872 10656 "GET /api/purchase/vendor-bills/108/ HTTP/1.1" 200 641
INFO 2025-07-10 17:03:41,794 basehttp 6872 10792 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16066
INFO 2025-07-10 17:03:41,837 basehttp 6872 9408 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:03:41,842 basehttp 6872 18284 "GET /api/sales/products/ HTTP/1.1" 200 7920
INFO 2025-07-10 17:03:41,851 basehttp 6872 10656 "GET /api/purchase/vendor-bills/108/ HTTP/1.1" 200 641
INFO 2025-07-10 17:03:41,966 basehttp 6872 9408 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:03:41,989 basehttp 6872 10792 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16066
INFO 2025-07-10 17:03:42,001 basehttp 6872 18284 "GET /api/sales/products/ HTTP/1.1" 200 7920
INFO 2025-07-10 17:03:42,012 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 17:03:42,123 basehttp 6872 10792 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16066
INFO 2025-07-10 17:03:42,132 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:03:42,192 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:03:42,270 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:03:42,336 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:03:42,391 basehttp 6872 17236 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:03:42,484 basehttp 6872 10792 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16066
INFO 2025-07-10 17:03:42,495 basehttp 6872 18284 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:03:42,584 basehttp 6872 10792 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:03:42,698 basehttp 6872 10792 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:03:42,726 basehttp 6872 17236 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:03:42,786 basehttp 6872 18284 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16066
INFO 2025-07-10 17:03:42,822 basehttp 6872 10792 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:04:02,287 basehttp 6872 10792 "PATCH /api/purchase/vendor-bills/108/ HTTP/1.1" 200 642
INFO 2025-07-10 17:04:03,951 basehttp 6872 18284 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:04:03,966 basehttp 6872 10792 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2379
INFO 2025-07-10 17:04:03,992 basehttp 6872 18284 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:04:04,006 basehttp 6872 10792 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2379
INFO 2025-07-10 17:04:18,654 basehttp 6872 10792 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:04:18,677 basehttp 6872 10792 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:04:18,703 basehttp 6872 18284 "GET /api/gl/journal-entries/ HTTP/1.1" 200 22239
INFO 2025-07-10 17:04:18,758 basehttp 6872 17236 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 17:04:18,805 basehttp 6872 18284 "GET /api/gl/journal-entries/ HTTP/1.1" 200 22239
INFO 2025-07-10 17:04:18,842 basehttp 6872 10792 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:17:45,054 autoreload 16028 1080 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:17:45,078 autoreload 6872 6064 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:17:47,934 autoreload 17884 14460 Watching for file changes with StatReloader
INFO 2025-07-10 17:17:47,961 autoreload 5588 9072 Watching for file changes with StatReloader
INFO 2025-07-10 17:18:31,310 autoreload 17884 14460 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:18:31,363 autoreload 5588 9072 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:18:33,617 autoreload 5060 16968 Watching for file changes with StatReloader
INFO 2025-07-10 17:18:33,639 autoreload 7728 13576 Watching for file changes with StatReloader
INFO 2025-07-10 17:18:58,224 autoreload 7728 13576 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:18:58,241 autoreload 5060 16968 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:19:00,289 autoreload 18092 18016 Watching for file changes with StatReloader
INFO 2025-07-10 17:19:00,309 autoreload 18192 18152 Watching for file changes with StatReloader
WARNING 2025-07-10 17:19:44,847 log 18092 14976 Bad Request: /api/purchase/vendor-bills/create_credit_from_return_note/
WARNING 2025-07-10 17:19:44,849 basehttp 18092 14976 "POST /api/purchase/vendor-bills/create_credit_from_return_note/ HTTP/1.1" 400 75
INFO 2025-07-10 17:23:42,283 basehttp 18092 4140 "GET /api/purchase/vendor-bills/ HTTP/1.1" 200 2379
INFO 2025-07-10 17:24:14,345 autoreload 18092 18016 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:24:15,476 autoreload 18192 18152 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:24:16,772 autoreload 12964 18100 Watching for file changes with StatReloader
INFO 2025-07-10 17:24:17,755 autoreload 14232 10248 Watching for file changes with StatReloader
INFO 2025-07-10 17:24:35,840 autoreload 12964 18100 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:24:36,889 autoreload 14232 10248 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:24:38,362 autoreload 6900 13500 Watching for file changes with StatReloader
INFO 2025-07-10 17:24:39,195 autoreload 796 17780 Watching for file changes with StatReloader
WARNING 2025-07-10 17:24:57,739 log 6900 17904 Bad Request: /api/purchase/vendor-bills/create_credit_from_return_note/
WARNING 2025-07-10 17:24:57,740 basehttp 6900 17904 "POST /api/purchase/vendor-bills/create_credit_from_return_note/ HTTP/1.1" 400 75
INFO 2025-07-10 17:25:39,376 basehttp 6900 17904 "GET /api/gl/journal-entries/ HTTP/1.1" 200 18262
INFO 2025-07-10 17:26:06,272 autoreload 796 17780 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:26:06,306 autoreload 6900 13500 D:\erp_accv1\accounting_software\accounting_software\erp_backend\purchase\models.py changed, reloading.
INFO 2025-07-10 17:26:08,938 autoreload 11284 13968 Watching for file changes with StatReloader
INFO 2025-07-10 17:26:08,947 autoreload 10656 16836 Watching for file changes with StatReloader
INFO 2025-07-10 17:26:19,637 basehttp 10656 6444 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-10 17:26:19,646 basehttp 10656 14116 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 17:26:19,661 basehttp 10656 13740 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-10 17:26:19,671 basehttp 10656 17340 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 17:26:19,719 basehttp 10656 14116 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 17:26:19,790 basehttp 10656 6444 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 17:26:19,808 basehttp 10656 14116 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:19,877 basehttp 10656 14116 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 17:26:19,877 basehttp 10656 6444 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 17:26:19,898 basehttp 10656 16812 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 17:26:20,009 basehttp 10656 14116 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:20,029 basehttp 10656 17340 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16066
INFO 2025-07-10 17:26:20,034 basehttp 10656 13740 "GET /api/sales/products/ HTTP/1.1" 200 7920
INFO 2025-07-10 17:26:20,147 basehttp 10656 16812 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:26:20,191 basehttp 10656 6444 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 17:26:20,272 basehttp 10656 15324 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 17:26:20,300 basehttp 10656 6444 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:26:20,390 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 18262
INFO 2025-07-10 17:26:20,421 basehttp 10656 15324 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:20,476 basehttp 10656 16812 "GET /api/sales/products/ HTTP/1.1" 200 7920
INFO 2025-07-10 17:26:20,587 basehttp 10656 15324 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:20,648 basehttp 10656 13740 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16066
INFO 2025-07-10 17:26:20,683 basehttp 10656 17340 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 17:26:20,743 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 18262
INFO 2025-07-10 17:26:20,851 basehttp 10656 6444 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 17:26:20,899 basehttp 10656 17340 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:26:26,222 basehttp 10656 15324 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:26,223 basehttp 10656 16812 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 17:26:26,249 basehttp 10656 13740 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 17:26:26,349 basehttp 10656 16812 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:26,496 basehttp 10656 6444 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 17:26:26,643 basehttp 10656 15324 "GET /api/sales/products/ HTTP/1.1" 200 7920
INFO 2025-07-10 17:26:26,664 basehttp 10656 13740 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 17:26:26,737 basehttp 10656 17340 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16066
INFO 2025-07-10 17:26:26,760 basehttp 10656 14116 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 17:26:26,904 basehttp 10656 16812 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 17:26:27,097 basehttp 10656 6444 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:27,230 basehttp 10656 17340 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:26:27,264 basehttp 10656 13740 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 17:26:27,321 basehttp 10656 17340 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:26:27,396 basehttp 10656 13740 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:26:27,403 basehttp 10656 15324 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2379
INFO 2025-07-10 17:26:27,419 basehttp 10656 14116 "GET /api/sales/products/ HTTP/1.1" 200 7920
INFO 2025-07-10 17:26:27,430 basehttp 10656 16812 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16066
INFO 2025-07-10 17:26:27,458 basehttp 10656 6444 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 17:26:27,478 basehttp 10656 13740 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2379
INFO 2025-07-10 17:26:32,812 basehttp 10656 13740 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 2
INFO 2025-07-10 17:26:32,852 basehttp 10656 13740 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 2
INFO 2025-07-10 17:26:34,420 basehttp 10656 13740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:26:34,520 basehttp 10656 6444 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3680
INFO 2025-07-10 17:26:34,529 basehttp 10656 13740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:26:34,641 basehttp 10656 6444 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3680
INFO 2025-07-10 17:26:39,655 basehttp 10656 13740 "OPTIONS /api/purchase/purchase-orders/ HTTP/1.1" 200 0
INFO 2025-07-10 17:26:39,655 basehttp 10656 16812 "OPTIONS /api/purchase/purchase-orders/ HTTP/1.1" 200 0
INFO 2025-07-10 17:26:39,664 basehttp 10656 6444 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:26:39,706 basehttp 10656 6444 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:26:39,736 basehttp 10656 16812 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 17:26:39,802 basehttp 10656 6444 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 17:30:23,301 basehttp 10656 6444 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:30:23,325 basehttp 10656 13740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:30:23,375 basehttp 10656 16812 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3680
INFO 2025-07-10 17:30:23,488 basehttp 10656 16812 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 3680
INFO 2025-07-10 17:30:26,244 basehttp 10656 16812 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:30:26,301 basehttp 10656 6444 "GET /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 4579
INFO 2025-07-10 17:30:26,301 basehttp 10656 16812 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:30:26,399 basehttp 10656 6444 "GET /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 4579
INFO 2025-07-10 17:30:26,496 basehttp 10656 13740 "GET /api/inventory/grn-returns/returnable_grns/ HTTP/1.1" 200 9487
INFO 2025-07-10 17:30:26,773 basehttp 10656 13740 "GET /api/inventory/grn-returns/returnable_grns/ HTTP/1.1" 200 9487
INFO 2025-07-10 17:30:44,598 basehttp 10656 13740 "POST /api/inventory/grn-returns/ HTTP/1.1" 201 495
INFO 2025-07-10 17:30:44,909 basehttp 10656 13740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:30:44,940 basehttp 10656 13740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:30:45,019 basehttp 10656 6444 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 4701
INFO 2025-07-10 17:30:45,144 basehttp 10656 6444 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 4701
INFO 2025-07-10 17:30:49,940 basehttp 10656 6444 "OPTIONS /api/inventory/grn-returns/4/approve/ HTTP/1.1" 200 0
INFO 2025-07-10 17:30:49,973 basehttp 10656 6444 "POST /api/inventory/grn-returns/4/approve/ HTTP/1.1" 200 47
INFO 2025-07-10 17:30:50,086 basehttp 10656 6444 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 4748
INFO 2025-07-10 17:30:55,020 basehttp 10656 6444 "OPTIONS /api/inventory/grn-returns/4/mark_returned/ HTTP/1.1" 200 0
INFO 2025-07-10 17:30:55,048 basehttp 10656 6444 "POST /api/inventory/grn-returns/4/mark_returned/ HTTP/1.1" 200 57
INFO 2025-07-10 17:30:55,156 basehttp 10656 6444 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 4748
INFO 2025-07-10 17:30:59,779 basehttp 10656 6444 "OPTIONS /api/inventory/grn-returns/4/post_to_inventory/ HTTP/1.1" 200 0
INFO 2025-07-10 17:31:00,027 basehttp 10656 6444 "POST /api/inventory/grn-returns/4/post_to_inventory/ HTTP/1.1" 200 58
INFO 2025-07-10 17:31:00,372 basehttp 10656 6444 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 4788
INFO 2025-07-10 17:31:04,873 basehttp 10656 13740 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:31:04,904 basehttp 10656 6444 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2379
INFO 2025-07-10 17:31:04,906 basehttp 10656 13740 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:31:04,933 basehttp 10656 16812 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2379
INFO 2025-07-10 17:31:06,170 basehttp 10656 16812 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 431
INFO 2025-07-10 17:31:06,199 basehttp 10656 16812 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 431
INFO 2025-07-10 17:31:09,754 basehttp 10656 16812 "POST /api/purchase/vendor-bills/create_credit_from_return_note/ HTTP/1.1" 201 173
INFO 2025-07-10 17:31:11,936 basehttp 10656 13740 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:31:11,962 basehttp 10656 16812 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2387
INFO 2025-07-10 17:31:12,005 basehttp 10656 13740 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:31:12,007 basehttp 10656 16812 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2387
INFO 2025-07-10 17:31:15,858 basehttp 10656 14116 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:31:15,874 basehttp 10656 6444 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:31:15,895 basehttp 10656 14116 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:31:15,895 basehttp 10656 17340 "OPTIONS /api/purchase/vendor-bills/110/ HTTP/1.1" 200 0
INFO 2025-07-10 17:31:15,903 basehttp 10656 17340 "OPTIONS /api/purchase/vendor-bills/110/ HTTP/1.1" 200 0
INFO 2025-07-10 17:31:15,967 basehttp 10656 15324 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 17:31:15,971 basehttp 10656 17340 "GET /api/purchase/vendor-bills/110/ HTTP/1.1" 200 736
INFO 2025-07-10 17:31:16,062 basehttp 10656 17340 "GET /api/purchase/vendor-bills/110/ HTTP/1.1" 200 736
INFO 2025-07-10 17:31:16,069 basehttp 10656 6444 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:31:16,082 basehttp 10656 13740 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:31:16,155 basehttp 10656 16812 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:31:16,173 basehttp 10656 15324 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 17:31:16,186 basehttp 10656 17340 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:31:16,193 basehttp 10656 14116 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 17:31:16,300 basehttp 10656 6444 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:31:16,310 basehttp 10656 14116 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 17:31:16,329 basehttp 10656 13740 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:31:16,400 basehttp 10656 6444 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:31:16,431 basehttp 10656 13740 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:31:16,483 basehttp 10656 6444 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:31:16,535 basehttp 10656 13740 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:31:16,603 basehttp 10656 13740 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:31:16,679 basehttp 10656 13740 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:31:16,825 basehttp 10656 14116 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:31:16,870 basehttp 10656 6444 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:31:16,892 basehttp 10656 13740 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 17:31:16,986 basehttp 10656 14116 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:31:17,088 basehttp 10656 6444 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:31:17,111 basehttp 10656 13740 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:31:31,368 basehttp 10656 13740 "PATCH /api/purchase/vendor-bills/110/ HTTP/1.1" 200 741
INFO 2025-07-10 17:31:33,019 basehttp 10656 6444 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:31:33,034 basehttp 10656 13740 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:31:33,052 basehttp 10656 6444 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:31:33,075 basehttp 10656 13740 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:31:45,808 basehttp 10656 13740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:31:45,825 basehttp 10656 13740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:31:45,860 basehttp 10656 6444 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17795
INFO 2025-07-10 17:31:45,893 basehttp 10656 14116 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:31:45,946 basehttp 10656 6444 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17795
INFO 2025-07-10 17:31:45,977 basehttp 10656 14116 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:31:52,814 basehttp 10656 14116 "OPTIONS /api/gl/journal-entries/203/ HTTP/1.1" 200 0
INFO 2025-07-10 17:31:52,909 basehttp 10656 14116 "PUT /api/gl/journal-entries/203/ HTTP/1.1" 200 1824
INFO 2025-07-10 17:31:53,065 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17797
INFO 2025-07-10 17:32:00,243 basehttp 10656 14116 "OPTIONS /api/gl/journal-entries/199/ HTTP/1.1" 200 0
INFO 2025-07-10 17:32:00,396 basehttp 10656 14116 "PUT /api/gl/journal-entries/199/ HTTP/1.1" 200 1776
INFO 2025-07-10 17:32:00,605 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17797
INFO 2025-07-10 17:32:06,500 basehttp 10656 14116 "OPTIONS /api/gl/journal-entries/200/ HTTP/1.1" 200 0
INFO 2025-07-10 17:32:06,614 basehttp 10656 14116 "PUT /api/gl/journal-entries/200/ HTTP/1.1" 200 1767
INFO 2025-07-10 17:32:06,779 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17797
INFO 2025-07-10 17:32:12,827 basehttp 10656 14116 "OPTIONS /api/gl/journal-entries/201/ HTTP/1.1" 200 0
INFO 2025-07-10 17:32:12,923 basehttp 10656 14116 "PUT /api/gl/journal-entries/201/ HTTP/1.1" 200 1795
INFO 2025-07-10 17:32:13,041 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17797
INFO 2025-07-10 17:32:49,109 basehttp 10656 14116 "OPTIONS /api/gl/journal-entries/202/ HTTP/1.1" 200 0
INFO 2025-07-10 17:32:49,214 basehttp 10656 14116 "PUT /api/gl/journal-entries/202/ HTTP/1.1" 200 1796
INFO 2025-07-10 17:32:49,418 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 18321
INFO 2025-07-10 17:32:55,392 basehttp 10656 14116 "PUT /api/gl/journal-entries/202/ HTTP/1.1" 200 1798
INFO 2025-07-10 17:32:55,529 basehttp 10656 14116 "GET /api/gl/journal-entries/ HTTP/1.1" 200 18323
INFO 2025-07-10 17:58:28,724 basehttp 10656 18944 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:58:28,727 basehttp 10656 2640 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 17:58:28,749 basehttp 10656 17484 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 17:58:28,781 basehttp 10656 19152 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 17:58:28,806 basehttp 10656 2640 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:58:28,857 basehttp 10656 2640 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 17:58:28,897 basehttp 10656 17484 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 17:58:28,936 basehttp 10656 19152 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 17:58:28,939 basehttp 10656 2640 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:58:28,959 basehttp 10656 17484 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:58:29,165 basehttp 10656 17484 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:58:29,173 basehttp 10656 18944 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 17:58:29,177 basehttp 10656 11632 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 17:58:29,271 basehttp 10656 3432 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 17:58:29,303 basehttp 10656 19152 "GET /api/gl/journal-entries/ HTTP/1.1" 200 18323
INFO 2025-07-10 17:58:29,310 basehttp 10656 17484 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 17:58:29,419 basehttp 10656 18944 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 17:58:29,490 basehttp 10656 11632 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 17:58:29,545 basehttp 10656 19152 "GET /api/gl/journal-entries/ HTTP/1.1" 200 18323
INFO 2025-07-10 17:58:29,588 basehttp 10656 2640 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 17:58:29,588 basehttp 10656 3432 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 17:58:29,654 basehttp 10656 19152 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:58:41,268 basehttp 10656 19152 "OPTIONS /api/contacts/vendors/?page=1 HTTP/1.1" 200 0
INFO 2025-07-10 17:58:41,268 basehttp 10656 3432 "OPTIONS /api/contacts/vendors/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 17:58:41,268 basehttp 10656 2640 "OPTIONS /api/contacts/vendors/?page=1 HTTP/1.1" 200 0
INFO 2025-07-10 17:58:41,269 basehttp 10656 11632 "OPTIONS /api/contacts/vendors/stats/ HTTP/1.1" 200 0
WARNING 2025-07-10 17:58:41,297 log 10656 3432 Not Found: /api/contacts/vendors/stats/
INFO 2025-07-10 17:58:41,308 basehttp 10656 19152 "GET /api/contacts/vendors/?page=1 HTTP/1.1" 200 8207
WARNING 2025-07-10 17:58:41,308 basehttp 10656 3432 "GET /api/contacts/vendors/stats/ HTTP/1.1" 404 23
INFO 2025-07-10 17:58:41,343 basehttp 10656 11632 "GET /api/contacts/vendors/?page=1 HTTP/1.1" 200 8207
WARNING 2025-07-10 17:58:41,347 log 10656 3432 Not Found: /api/contacts/vendors/stats/
WARNING 2025-07-10 17:58:41,352 basehttp 10656 3432 "GET /api/contacts/vendors/stats/ HTTP/1.1" 404 23
INFO 2025-07-10 17:58:46,588 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:58:46,603 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:58:46,620 basehttp 10656 11632 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 17:58:46,650 basehttp 10656 3432 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 17:58:48,211 basehttp 10656 11632 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:58:48,220 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:58:48,238 basehttp 10656 11632 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:58:48,255 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:58:52,598 basehttp 10656 3432 "OPTIONS /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 0
INFO 2025-07-10 17:58:52,598 basehttp 10656 11632 "OPTIONS /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 0
INFO 2025-07-10 17:58:52,701 basehttp 10656 3432 "GET /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 4372
INFO 2025-07-10 17:58:52,818 basehttp 10656 3432 "GET /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 4372
INFO 2025-07-10 17:59:00,123 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:00,156 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:00,166 basehttp 10656 11632 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:59:00,228 basehttp 10656 11632 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:59:07,960 basehttp 10656 11632 "GET /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 4372
INFO 2025-07-10 17:59:08,153 basehttp 10656 3432 "GET /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 4372
INFO 2025-07-10 17:59:09,537 basehttp 10656 11632 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:09,558 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:09,576 basehttp 10656 11632 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:09,603 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:12,894 basehttp 10656 2640 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:12,916 basehttp 10656 2640 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:12,925 basehttp 10656 19152 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:59:13,002 basehttp 10656 19152 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:59:13,011 basehttp 10656 11632 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:59:13,047 basehttp 10656 18944 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 17:59:13,094 basehttp 10656 3432 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:59:13,128 basehttp 10656 11632 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35137
INFO 2025-07-10 17:59:13,216 basehttp 10656 18944 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 17:59:13,268 basehttp 10656 19152 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:59:13,282 basehttp 10656 3432 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:59:13,365 basehttp 10656 18944 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16065
INFO 2025-07-10 17:59:13,394 basehttp 10656 19152 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 17:59:13,466 basehttp 10656 18944 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:59:13,525 basehttp 10656 19152 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 17:59:40,777 basehttp 10656 18944 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:40,787 basehttp 10656 19152 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:40,806 basehttp 10656 18944 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:40,827 basehttp 10656 19152 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:42,898 basehttp 10656 19152 "OPTIONS /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 0
INFO 2025-07-10 17:59:42,898 basehttp 10656 18944 "OPTIONS /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 0
INFO 2025-07-10 17:59:42,974 basehttp 10656 18944 "GET /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 2
INFO 2025-07-10 17:59:43,094 basehttp 10656 18944 "GET /api/purchase/purchase-orders/billable_service_pos/ HTTP/1.1" 200 2
INFO 2025-07-10 17:59:44,688 basehttp 10656 18944 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:44,715 basehttp 10656 19152 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 17:59:44,723 basehttp 10656 18944 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:44,748 basehttp 10656 3432 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 17:59:49,390 basehttp 10656 18944 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:49,416 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:49,417 basehttp 10656 18944 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:49,446 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:50,931 basehttp 10656 3432 "OPTIONS /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 0
INFO 2025-07-10 17:59:50,932 basehttp 10656 18944 "OPTIONS /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 0
INFO 2025-07-10 17:59:50,975 basehttp 10656 18944 "GET /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 5383
INFO 2025-07-10 17:59:51,028 basehttp 10656 3432 "GET /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 5383
INFO 2025-07-10 17:59:55,171 basehttp 10656 18944 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:55,187 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:55,207 basehttp 10656 18944 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 17:59:55,237 basehttp 10656 3432 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 17:59:56,570 basehttp 10656 3432 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 2
INFO 2025-07-10 17:59:56,592 basehttp 10656 3432 "GET /api/purchase/purchase-orders/billable_return_notes/ HTTP/1.1" 200 2
INFO 2025-07-10 17:59:58,747 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:58,774 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 17:59:58,843 basehttp 10656 18944 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 4788
INFO 2025-07-10 17:59:58,936 basehttp 10656 3432 "GET /api/inventory/grn-returns/?page=1 HTTP/1.1" 200 4788
INFO 2025-07-10 18:00:06,986 basehttp 10656 2640 "OPTIONS /api/inventory/inventory/? HTTP/1.1" 200 0
INFO 2025-07-10 18:00:06,986 basehttp 10656 19152 "OPTIONS /api/inventory/inventory/? HTTP/1.1" 200 0
INFO 2025-07-10 18:00:06,986 basehttp 10656 18944 "OPTIONS /api/inventory/stock-transactions/? HTTP/1.1" 200 0
INFO 2025-07-10 18:00:06,986 basehttp 10656 11632 "OPTIONS /api/inventory/stock-transactions/? HTTP/1.1" 200 0
INFO 2025-07-10 18:00:06,999 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:00:07,035 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:00:07,088 basehttp 10656 19152 "GET /api/inventory/inventory/? HTTP/1.1" 200 4396
INFO 2025-07-10 18:00:07,191 basehttp 10656 3432 "GET /api/inventory/inventory/? HTTP/1.1" 200 4396
INFO 2025-07-10 18:00:07,206 basehttp 10656 11632 "GET /api/inventory/stock-transactions/? HTTP/1.1" 200 4751
INFO 2025-07-10 18:00:07,288 basehttp 10656 3432 "GET /api/inventory/stock-transactions/? HTTP/1.1" 200 4751
INFO 2025-07-10 18:00:35,618 basehttp 10656 3432 "OPTIONS /api/inventory/stock-transactions/?no_pagination=true HTTP/1.1" 200 0
INFO 2025-07-10 18:00:35,618 basehttp 10656 11632 "OPTIONS /api/inventory/stock-transactions/?no_pagination=true HTTP/1.1" 200 0
INFO 2025-07-10 18:00:35,931 basehttp 10656 11632 "GET /api/inventory/stock-transactions/?no_pagination=true HTTP/1.1" 200 19963
INFO 2025-07-10 18:00:36,219 basehttp 10656 3432 "GET /api/inventory/stock-transactions/?no_pagination=true HTTP/1.1" 200 19963
INFO 2025-07-10 18:00:46,532 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:00:46,555 basehttp 10656 3432 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:00:46,620 basehttp 10656 19152 "GET /api/inventory/inventory/? HTTP/1.1" 200 4396
INFO 2025-07-10 18:00:46,638 basehttp 10656 11632 "GET /api/inventory/stock-transactions/? HTTP/1.1" 200 4751
INFO 2025-07-10 18:00:46,734 basehttp 10656 3432 "GET /api/inventory/inventory/? HTTP/1.1" 200 4396
INFO 2025-07-10 18:00:46,751 basehttp 10656 11632 "GET /api/inventory/stock-transactions/? HTTP/1.1" 200 4751
INFO 2025-07-10 18:01:21,564 basehttp 10656 11632 "GET /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 4372
INFO 2025-07-10 18:01:21,685 basehttp 10656 11632 "GET /api/purchase/purchase-orders/billable_receipts/ HTTP/1.1" 200 4372
INFO 2025-07-10 18:07:52,634 basehttp 10656 16504 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:07:52,650 basehttp 10656 20304 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:07:52,650 basehttp 10656 19072 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:07:52,664 basehttp 10656 18824 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:07:52,719 basehttp 10656 18824 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:07:52,719 basehttp 10656 19072 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:07:52,750 basehttp 10656 20304 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:07:52,774 basehttp 10656 18824 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:07:52,801 basehttp 10656 18824 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:07:52,801 basehttp 10656 16504 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:07:52,834 basehttp 10656 19072 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:07:52,851 basehttp 10656 8708 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:07:52,851 basehttp 10656 19776 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:07:52,919 basehttp 10656 18824 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:07:52,990 basehttp 10656 19072 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:07:53,019 basehttp 10656 19776 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:09:48,989 basehttp 10656 10188 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:09:49,033 basehttp 10656 6204 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:09:49,038 basehttp 10656 14456 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:09:49,070 basehttp 10656 15864 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:09:49,128 basehttp 10656 14456 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:09:49,168 basehttp 10656 6204 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:09:49,215 basehttp 10656 15864 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:09:49,224 basehttp 10656 10188 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:09:49,263 basehttp 10656 14456 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:09:49,295 basehttp 10656 6204 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:09:49,324 basehttp 10656 16552 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:09:49,326 basehttp 10656 7820 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:09:49,399 basehttp 10656 15864 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:09:49,433 basehttp 10656 6204 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:09:49,627 basehttp 10656 14456 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:09:49,646 basehttp 10656 10188 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:09:57,040 basehttp 10656 14456 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:09:57,049 basehttp 10656 10188 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:09:57,145 basehttp 10656 15864 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:09:57,162 basehttp 10656 10188 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:10:00,492 basehttp 10656 15864 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:10:00,492 basehttp 10656 10188 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:10:00,565 basehttp 10656 15864 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:10:00,613 basehttp 10656 10188 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:10:44,509 basehttp 10656 16552 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:44,513 basehttp 10656 7820 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:10:44,518 basehttp 10656 6204 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:10:44,533 basehttp 10656 15864 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:10:44,664 basehttp 10656 16552 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:44,676 basehttp 10656 6204 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:10:44,713 basehttp 10656 7820 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:10:44,757 basehttp 10656 10188 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:10:44,776 basehttp 10656 15864 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:10:44,930 basehttp 10656 14456 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:10:45,064 basehttp 10656 16552 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:10:45,090 basehttp 10656 6204 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:10:45,268 basehttp 10656 6204 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:45,360 basehttp 10656 15864 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:10:45,412 basehttp 10656 6204 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:45,443 basehttp 10656 10188 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:10:55,588 basehttp 10656 16552 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:10:55,603 basehttp 10656 7820 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:10:55,603 basehttp 10656 14456 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:55,622 basehttp 10656 6204 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:10:55,818 basehttp 10656 14456 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:55,831 basehttp 10656 7820 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:10:55,922 basehttp 10656 16552 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:10:55,923 basehttp 10656 15864 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:10:55,949 basehttp 10656 10188 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:10:55,969 basehttp 10656 6204 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:10:56,097 basehttp 10656 7820 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:56,130 basehttp 10656 14456 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:10:56,456 basehttp 10656 16552 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:10:56,482 basehttp 10656 10188 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:10:56,732 basehttp 10656 6204 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:10:56,742 basehttp 10656 14456 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:10:56,762 basehttp 10656 10188 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:10:56,787 basehttp 10656 7820 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:10:56,792 basehttp 10656 15864 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:10:56,978 basehttp 10656 7820 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:12:27,531 basehttp 10656 15864 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:12:27,542 basehttp 10656 7820 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:12:27,565 basehttp 10656 15864 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:12:27,599 basehttp 10656 7820 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2388
INFO 2025-07-10 18:14:24,533 basehttp 10656 14456 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:14:24,539 basehttp 10656 10188 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:14:24,549 basehttp 10656 16552 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:14:24,563 basehttp 10656 15864 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:14:24,813 basehttp 10656 6204 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:14:24,813 basehttp 10656 7820 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:14:24,877 basehttp 10656 16552 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:14:24,895 basehttp 10656 10188 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:14:24,953 basehttp 10656 15864 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:14:24,958 basehttp 10656 14456 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:14:25,088 basehttp 10656 7820 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:14:25,147 basehttp 10656 6204 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16065
INFO 2025-07-10 18:14:25,176 basehttp 10656 10188 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:14:25,283 basehttp 10656 15864 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:14:25,293 basehttp 10656 16552 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:14:25,471 basehttp 10656 16552 "GET /api/account/company/current/ HTTP/1.1" 200 655
WARNING 2025-07-10 18:14:59,000 log 10656 15864 Not Found: /api/contacts/vendors/stats/
WARNING 2025-07-10 18:14:59,001 basehttp 10656 15864 "GET /api/contacts/vendors/stats/ HTTP/1.1" 404 23
INFO 2025-07-10 18:14:59,015 basehttp 10656 16552 "GET /api/contacts/vendors/?page=1 HTTP/1.1" 200 8207
WARNING 2025-07-10 18:14:59,027 log 10656 15864 Not Found: /api/contacts/vendors/stats/
WARNING 2025-07-10 18:14:59,038 basehttp 10656 15864 "GET /api/contacts/vendors/stats/ HTTP/1.1" 404 23
INFO 2025-07-10 18:14:59,049 basehttp 10656 10188 "GET /api/contacts/vendors/?page=1 HTTP/1.1" 200 8207
INFO 2025-07-10 18:15:27,590 basehttp 10656 10188 "OPTIONS /api/contacts/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 18:15:27,643 basehttp 10656 10188 "POST /api/contacts/vendors/ HTTP/1.1" 201 813
INFO 2025-07-10 18:15:29,209 basehttp 10656 10188 "GET /api/contacts/vendors/?page=1 HTTP/1.1" 200 8207
WARNING 2025-07-10 18:15:29,214 log 10656 15864 Not Found: /api/contacts/vendors/stats/
WARNING 2025-07-10 18:15:29,216 basehttp 10656 15864 "GET /api/contacts/vendors/stats/ HTTP/1.1" 404 23
INFO 2025-07-10 18:21:40,246 basehttp 10656 19856 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:21:40,317 basehttp 10656 19856 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:22:20,882 basehttp 10656 19856 "POST /api/sales/products/ HTTP/1.1" 201 740
INFO 2025-07-10 18:22:22,532 basehttp 10656 19856 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:22:22,696 basehttp 10656 19856 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:22:34,107 basehttp 10656 19856 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:22:34,136 basehttp 10656 19512 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 18:22:34,144 basehttp 10656 19856 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:22:34,175 basehttp 10656 19512 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12383
INFO 2025-07-10 18:22:35,987 basehttp 10656 19512 "OPTIONS /api/contacts/vendors/?page=1&status=active HTTP/1.1" 200 0
INFO 2025-07-10 18:22:36,018 basehttp 10656 19512 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:22:36,037 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:22:36,056 basehttp 10656 19856 "GET /api/contacts/vendors/?page=1&status=active HTTP/1.1" 200 8221
INFO 2025-07-10 18:22:36,058 basehttp 10656 19512 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:22:36,139 basehttp 10656 19512 "GET /api/contacts/vendors/?page=1&status=active HTTP/1.1" 200 8221
INFO 2025-07-10 18:22:36,188 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:22:36,416 basehttp 10656 19512 "OPTIONS /api/contacts/vendors/?page=2&status=active HTTP/1.1" 200 0
INFO 2025-07-10 18:22:36,416 basehttp 10656 18480 "OPTIONS /api/contacts/vendors/?page=2&status=active HTTP/1.1" 200 0
INFO 2025-07-10 18:22:36,443 basehttp 10656 19512 "GET /api/contacts/vendors/?page=2&status=active HTTP/1.1" 200 8333
INFO 2025-07-10 18:22:36,464 basehttp 10656 18480 "GET /api/contacts/vendors/?page=2&status=active HTTP/1.1" 200 8333
INFO 2025-07-10 18:22:36,572 basehttp 10656 18480 "OPTIONS /api/contacts/vendors/?page=3&status=active HTTP/1.1" 200 0
INFO 2025-07-10 18:22:36,606 basehttp 10656 18480 "GET /api/contacts/vendors/?page=3&status=active HTTP/1.1" 200 8120
INFO 2025-07-10 18:22:36,628 basehttp 10656 18480 "OPTIONS /api/contacts/vendors/?page=4&status=active HTTP/1.1" 200 0
INFO 2025-07-10 18:22:36,636 basehttp 10656 19512 "GET /api/contacts/vendors/?page=3&status=active HTTP/1.1" 200 8120
INFO 2025-07-10 18:22:36,674 basehttp 10656 18480 "GET /api/contacts/vendors/?page=4&status=active HTTP/1.1" 200 8591
INFO 2025-07-10 18:22:36,696 basehttp 10656 19512 "GET /api/contacts/vendors/?page=4&status=active HTTP/1.1" 200 8591
INFO 2025-07-10 18:22:36,705 basehttp 10656 18480 "OPTIONS /api/contacts/vendors/?page=5&status=active HTTP/1.1" 200 0
INFO 2025-07-10 18:22:36,725 basehttp 10656 19512 "GET /api/contacts/vendors/?page=5&status=active HTTP/1.1" 200 3383
INFO 2025-07-10 18:22:36,742 basehttp 10656 19512 "GET /api/contacts/vendors/?page=5&status=active HTTP/1.1" 200 3383
INFO 2025-07-10 18:23:30,090 basehttp 10656 19512 "POST /api/purchase/purchase-orders/ HTTP/1.1" 201 1060
INFO 2025-07-10 18:23:31,710 basehttp 10656 19512 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12029
INFO 2025-07-10 18:23:34,519 basehttp 10656 19512 "OPTIONS /api/purchase/purchase-orders/90/send/ HTTP/1.1" 200 0
INFO 2025-07-10 18:23:34,558 basehttp 10656 19512 "POST /api/purchase/purchase-orders/90/send/ HTTP/1.1" 200 32
INFO 2025-07-10 18:23:34,631 basehttp 10656 19512 "GET /api/purchase/purchase-orders/ HTTP/1.1" 200 12028
INFO 2025-07-10 18:23:39,866 basehttp 10656 18480 "OPTIONS /api/inventory/grns/?page=1 HTTP/1.1" 200 0
INFO 2025-07-10 18:23:39,867 basehttp 10656 19856 "OPTIONS /api/inventory/grns/?page=1 HTTP/1.1" 200 0
INFO 2025-07-10 18:23:39,876 basehttp 10656 19512 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:23:39,901 basehttp 10656 19512 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:23:40,029 basehttp 10656 19856 "GET /api/inventory/grns/?page=1 HTTP/1.1" 200 8781
INFO 2025-07-10 18:23:40,146 basehttp 10656 19512 "GET /api/inventory/grns/?page=1 HTTP/1.1" 200 8781
INFO 2025-07-10 18:23:41,833 basehttp 10656 19512 "OPTIONS /api/inventory/grns/pending_pos/ HTTP/1.1" 200 0
INFO 2025-07-10 18:23:41,834 basehttp 10656 19856 "OPTIONS /api/sales/products/dropdown/ HTTP/1.1" 200 0
INFO 2025-07-10 18:23:41,839 basehttp 10656 19512 "OPTIONS /api/inventory/grns/pending_pos/ HTTP/1.1" 200 0
INFO 2025-07-10 18:23:41,846 basehttp 10656 19856 "OPTIONS /api/sales/products/dropdown/ HTTP/1.1" 200 0
INFO 2025-07-10 18:23:41,898 basehttp 10656 19856 "GET /api/sales/products/dropdown/ HTTP/1.1" 200 4137
INFO 2025-07-10 18:23:41,931 basehttp 10656 18480 "GET /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 4579
INFO 2025-07-10 18:23:41,952 basehttp 10656 19856 "GET /api/sales/products/dropdown/ HTTP/1.1" 200 4137
INFO 2025-07-10 18:23:41,969 basehttp 10656 19512 "GET /api/inventory/grns/pending_pos/ HTTP/1.1" 200 3602
INFO 2025-07-10 18:23:42,021 basehttp 10656 18480 "GET /api/inventory/warehouses/?is_active=true&page_size=100 HTTP/1.1" 200 4579
INFO 2025-07-10 18:23:42,038 basehttp 10656 19512 "GET /api/inventory/grns/pending_pos/ HTTP/1.1" 200 3602
INFO 2025-07-10 18:23:53,305 basehttp 10656 19512 "OPTIONS /api/purchase/purchase-orders/90/ HTTP/1.1" 200 0
INFO 2025-07-10 18:23:53,326 basehttp 10656 19512 "GET /api/purchase/purchase-orders/90/ HTTP/1.1" 200 1059
INFO 2025-07-10 18:24:00,726 basehttp 10656 19512 "OPTIONS /api/inventory/grns/ HTTP/1.1" 200 0
INFO 2025-07-10 18:24:00,804 basehttp 10656 19512 "POST /api/inventory/grns/ HTTP/1.1" 201 366
INFO 2025-07-10 18:24:02,509 basehttp 10656 19512 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:02,557 basehttp 10656 19512 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:02,717 basehttp 10656 18480 "GET /api/inventory/grns/?page=1 HTTP/1.1" 200 8756
INFO 2025-07-10 18:24:02,903 basehttp 10656 19512 "GET /api/inventory/grns/?page=1 HTTP/1.1" 200 8756
INFO 2025-07-10 18:24:05,764 basehttp 10656 19512 "OPTIONS /api/inventory/grns/64/ HTTP/1.1" 200 0
INFO 2025-07-10 18:24:05,824 basehttp 10656 19512 "PATCH /api/inventory/grns/64/ HTTP/1.1" 200 707
INFO 2025-07-10 18:24:05,966 basehttp 10656 19512 "GET /api/inventory/grns/?page=1 HTTP/1.1" 200 8759
INFO 2025-07-10 18:24:09,541 basehttp 10656 19512 "OPTIONS /api/inventory/grns/64/post_to_inventory/ HTTP/1.1" 200 0
INFO 2025-07-10 18:24:09,700 basehttp 10656 19512 "POST /api/inventory/grns/64/post_to_inventory/ HTTP/1.1" 200 50
INFO 2025-07-10 18:24:09,895 basehttp 10656 19512 "GET /api/inventory/grns/?page=1 HTTP/1.1" 200 8779
INFO 2025-07-10 18:24:12,566 basehttp 10656 19512 "GET /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 5770
INFO 2025-07-10 18:24:12,624 basehttp 10656 18480 "GET /api/purchase/purchase-orders/billable_grns/ HTTP/1.1" 200 5770
INFO 2025-07-10 18:24:14,084 basehttp 10656 18480 "OPTIONS /api/purchase/vendor-bills/create_from_grn/ HTTP/1.1" 200 0
INFO 2025-07-10 18:24:14,197 basehttp 10656 18480 "POST /api/purchase/vendor-bills/create_from_grn/ HTTP/1.1" 201 123
INFO 2025-07-10 18:24:16,389 basehttp 10656 18480 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2396
INFO 2025-07-10 18:24:16,392 basehttp 10656 19512 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:24:16,426 basehttp 10656 19856 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2396
INFO 2025-07-10 18:24:16,426 basehttp 10656 19512 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:24:19,796 basehttp 10656 9496 "OPTIONS /api/purchase/vendor-bills/111/ HTTP/1.1" 200 0
INFO 2025-07-10 18:24:19,812 basehttp 10656 5740 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35951
INFO 2025-07-10 18:24:19,868 basehttp 10656 16652 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:19,916 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:19,928 basehttp 10656 19856 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:19,974 basehttp 10656 19512 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:24:19,989 basehttp 10656 5740 "GET /api/purchase/vendor-bills/111/ HTTP/1.1" 200 642
INFO 2025-07-10 18:24:20,005 basehttp 10656 18480 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:20,074 basehttp 10656 16652 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35951
INFO 2025-07-10 18:24:20,118 basehttp 10656 9496 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:24:20,145 basehttp 10656 19512 "GET /api/purchase/vendor-bills/111/ HTTP/1.1" 200 642
INFO 2025-07-10 18:24:20,145 basehttp 10656 19856 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:20,277 basehttp 10656 9496 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:20,282 basehttp 10656 5740 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:20,334 basehttp 10656 16652 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:24:20,477 basehttp 10656 9496 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:20,485 basehttp 10656 18480 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:24:20,546 basehttp 10656 18480 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:20,609 basehttp 10656 9496 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:20,698 basehttp 10656 9496 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:20,768 basehttp 10656 16652 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:20,807 basehttp 10656 18480 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:20,876 basehttp 10656 9496 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:20,918 basehttp 10656 16652 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:20,979 basehttp 10656 16652 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:21,072 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:21,159 basehttp 10656 16652 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:24:21,183 basehttp 10656 9496 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:34,090 basehttp 10656 9496 "PATCH /api/purchase/vendor-bills/111/ HTTP/1.1" 200 642
INFO 2025-07-10 18:24:35,796 basehttp 10656 16652 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:24:35,807 basehttp 10656 9496 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2396
INFO 2025-07-10 18:24:35,831 basehttp 10656 16652 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:24:35,852 basehttp 10656 9496 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2396
INFO 2025-07-10 18:24:39,659 basehttp 10656 5740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:39,703 basehttp 10656 5740 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:39,721 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:39,724 basehttp 10656 19856 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35951
INFO 2025-07-10 18:24:39,751 basehttp 10656 19512 "GET /api/purchase/vendor-bills/111/ HTTP/1.1" 200 642
INFO 2025-07-10 18:24:39,813 basehttp 10656 19512 "GET /api/purchase/vendor-bills/111/ HTTP/1.1" 200 642
INFO 2025-07-10 18:24:39,854 basehttp 10656 16652 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:39,854 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:39,896 basehttp 10656 19856 "GET /api/contacts/vendors/?page_size=100 HTTP/1.1" 200 35951
INFO 2025-07-10 18:24:39,938 basehttp 10656 5740 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:24:40,012 basehttp 10656 9496 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:24:40,015 basehttp 10656 16652 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:40,103 basehttp 10656 5740 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:24:40,119 basehttp 10656 18480 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:40,166 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:40,230 basehttp 10656 5740 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:40,298 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:24:40,401 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:40,472 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:40,512 basehttp 10656 5740 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:40,545 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:24:40,615 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:40,680 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:40,742 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47846
INFO 2025-07-10 18:24:40,862 basehttp 10656 18480 "GET /api/sales-tax/?tax_type=input HTTP/1.1" 200 1454
INFO 2025-07-10 18:24:40,934 basehttp 10656 5740 "GET /api/sales/products/?page_size=100 HTTP/1.1" 200 16808
INFO 2025-07-10 18:24:40,945 basehttp 10656 19856 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:45,815 basehttp 10656 19856 "PATCH /api/purchase/vendor-bills/111/ HTTP/1.1" 200 641
INFO 2025-07-10 18:24:47,507 basehttp 10656 5740 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:24:47,526 basehttp 10656 19856 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2395
INFO 2025-07-10 18:24:47,542 basehttp 10656 5740 "GET /api/purchase/vendor-bills/stats/ HTTP/1.1" 200 131
INFO 2025-07-10 18:24:47,566 basehttp 10656 19856 "GET /api/purchase/vendor-bills/? HTTP/1.1" 200 2395
INFO 2025-07-10 18:24:58,811 basehttp 10656 19856 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:58,834 basehttp 10656 19856 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:24:58,868 basehttp 10656 5740 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17823
INFO 2025-07-10 18:24:58,898 basehttp 10656 18480 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:24:58,960 basehttp 10656 5740 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17823
INFO 2025-07-10 18:24:59,000 basehttp 10656 18480 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:25:13,688 basehttp 10656 18480 "OPTIONS /api/gl/journal-entries/204/ HTTP/1.1" 200 0
INFO 2025-07-10 18:25:13,794 basehttp 10656 18480 "PUT /api/gl/journal-entries/204/ HTTP/1.1" 200 1775
INFO 2025-07-10 18:25:13,942 basehttp 10656 18480 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17825
INFO 2025-07-10 18:41:03,168 basehttp 10656 19816 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:41:03,168 basehttp 10656 14500 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 18:41:03,292 basehttp 10656 13512 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:41:03,318 basehttp 10656 14824 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:41:03,320 basehttp 10656 18924 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:41:03,374 basehttp 10656 19816 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:41:03,381 basehttp 10656 14500 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:41:03,425 basehttp 10656 18956 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:41:03,542 basehttp 10656 14824 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:41:03,591 basehttp 10656 18924 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:41:03,766 basehttp 10656 13512 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 18:41:03,836 basehttp 10656 19816 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:41:03,864 basehttp 10656 14500 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:41:04,259 basehttp 10656 14824 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17825
INFO 2025-07-10 18:41:04,261 basehttp 10656 18956 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:41:04,261 basehttp 10656 18924 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:41:04,377 basehttp 10656 19816 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:41:04,403 basehttp 10656 13512 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47847
INFO 2025-07-10 18:41:04,426 basehttp 10656 14500 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:41:04,883 basehttp 10656 18924 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17825
INFO 2025-07-10 18:41:04,941 basehttp 10656 18956 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:41:04,972 basehttp 10656 19816 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:59:21,413 basehttp 10656 13796 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:21,531 basehttp 10656 13796 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:21,531 basehttp 10656 10688 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:21,608 basehttp 10656 13796 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:21,608 basehttp 10656 16412 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:21,708 basehttp 10656 10688 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:21,716 basehttp 10656 13796 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:21,789 basehttp 10656 16804 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:22,013 basehttp 10656 2076 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:22,033 basehttp 10656 10688 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:59:22,104 basehttp 10656 16412 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 18:59:22,176 basehttp 10656 13796 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:59:22,184 basehttp 10656 16804 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:59:22,186 basehttp 10656 4644 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:22,631 basehttp 10656 10688 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:59:22,641 basehttp 10656 2076 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:59:22,844 basehttp 10656 16412 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 18:59:22,885 basehttp 10656 13796 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 18:59:22,895 basehttp 10656 16804 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:59:23,032 basehttp 10656 4644 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:59:23,148 basehttp 10656 10688 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 18:59:23,352 basehttp 10656 2076 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 18:59:23,393 basehttp 10656 16412 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:59:23,549 basehttp 10656 13796 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:23,939 basehttp 10656 4644 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 18:59:23,971 basehttp 10656 10688 "OPTIONS /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:24,002 basehttp 10656 16804 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 18:59:24,036 basehttp 10656 2076 "OPTIONS /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:24,214 basehttp 10656 16412 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17825
INFO 2025-07-10 18:59:24,219 basehttp 10656 13796 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 18:59:24,267 basehttp 10656 10688 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 18:59:24,368 basehttp 10656 4644 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 18:59:24,378 basehttp 10656 2076 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:59:24,496 basehttp 10656 16804 "GET /api/gl/journal-entries/ HTTP/1.1" 200 17825
INFO 2025-07-10 18:59:24,570 basehttp 10656 10688 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 18:59:24,789 basehttp 10656 2076 "GET /api/gl/chart-of-accounts-fast/ HTTP/1.1" 200 47848
INFO 2025-07-10 19:08:16,295 autoreload 10656 16836 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:08:16,420 autoreload 11284 13968 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:08:19,236 autoreload 3032 19712 Watching for file changes with StatReloader
INFO 2025-07-10 19:08:19,268 autoreload 12964 18948 Watching for file changes with StatReloader
INFO 2025-07-10 19:10:30,536 autoreload 12964 18948 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:10:30,536 autoreload 3032 19712 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:10:32,577 autoreload 16300 6008 Watching for file changes with StatReloader
INFO 2025-07-10 19:10:32,608 autoreload 18388 15312 Watching for file changes with StatReloader
INFO 2025-07-10 19:10:52,770 autoreload 16300 6008 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:10:52,785 autoreload 18388 15312 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:10:54,811 autoreload 15676 19588 Watching for file changes with StatReloader
INFO 2025-07-10 19:10:54,832 autoreload 19636 6880 Watching for file changes with StatReloader
INFO 2025-07-10 19:11:49,537 autoreload 19636 6880 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:11:49,552 autoreload 15676 19588 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:11:51,953 autoreload 18176 20052 Watching for file changes with StatReloader
INFO 2025-07-10 19:11:51,985 autoreload 10264 17644 Watching for file changes with StatReloader
INFO 2025-07-10 19:12:33,838 autoreload 10264 17644 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:12:33,860 autoreload 18176 20052 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:12:35,740 autoreload 2420 8472 Watching for file changes with StatReloader
INFO 2025-07-10 19:12:35,749 autoreload 17472 212 Watching for file changes with StatReloader
INFO 2025-07-10 19:13:17,340 autoreload 17472 212 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:13:17,592 autoreload 2420 8472 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:13:20,643 autoreload 18716 19540 Watching for file changes with StatReloader
INFO 2025-07-10 19:13:20,799 autoreload 21208 21132 Watching for file changes with StatReloader
INFO 2025-07-10 19:13:42,853 autoreload 18716 19540 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:13:42,884 autoreload 21208 21132 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:13:45,227 autoreload 10384 4588 Watching for file changes with StatReloader
INFO 2025-07-10 19:13:45,264 autoreload 18372 18240 Watching for file changes with StatReloader
INFO 2025-07-10 19:14:25,599 autoreload 10384 4588 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:14:25,719 autoreload 18372 18240 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:14:28,817 autoreload 19532 2004 Watching for file changes with StatReloader
INFO 2025-07-10 19:14:28,824 autoreload 9724 17372 Watching for file changes with StatReloader
INFO 2025-07-10 19:14:43,666 autoreload 9724 17372 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:14:43,748 autoreload 19532 2004 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:14:46,630 autoreload 18104 9760 Watching for file changes with StatReloader
INFO 2025-07-10 19:14:46,635 autoreload 15124 14888 Watching for file changes with StatReloader
INFO 2025-07-10 19:17:20,890 autoreload 15124 14888 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:17:21,169 autoreload 18104 9760 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 19:17:23,365 autoreload 18560 20256 Watching for file changes with StatReloader
INFO 2025-07-10 19:17:23,505 autoreload 10140 18164 Watching for file changes with StatReloader
INFO 2025-07-10 20:00:38,920 basehttp 18560 21456 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-10 20:00:39,208 basehttp 18560 21456 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4181
INFO 2025-07-10 20:00:46,610 basehttp 18560 21456 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-10 20:00:52,669 basehttp 18560 21456 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-10 20:00:52,837 basehttp 18560 21456 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 20:00:59,501 basehttp 18560 21456 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 20:01:02,766 basehttp 18560 21456 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 20:07:33,219 basehttp 18560 14780 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 20:08:28,037 basehttp 18560 14780 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 20:16:23,515 basehttp 18560 11276 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 20:26:13,203 basehttp 18560 9036 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,229 basehttp 18560 9036 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,234 basehttp 18560 20680 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,253 basehttp 18560 20680 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,254 basehttp 18560 9036 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,254 basehttp 18560 14612 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,270 basehttp 18560 9036 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,270 basehttp 18560 20680 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,270 basehttp 18560 9684 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,270 basehttp 18560 14612 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,301 basehttp 18560 9684 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,302 basehttp 18560 20680 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,304 basehttp 18560 13532 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,388 basehttp 18560 16236 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 20:26:13,418 basehttp 18560 20680 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:26:13,418 basehttp 18560 13532 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:13,483 basehttp 18560 9036 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:26:13,522 basehttp 18560 16236 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:26:13,547 basehttp 18560 13532 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:26:13,579 basehttp 18560 9036 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:13,694 basehttp 18560 13532 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:26:13,705 basehttp 18560 9036 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:13,713 basehttp 18560 20680 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:26:13,793 basehttp 18560 14612 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:26:13,793 basehttp 18560 16236 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:26:13,854 basehttp 18560 9684 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:26:13,893 basehttp 18560 9036 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:26:14,009 basehttp 18560 14612 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:26:14,043 basehttp 18560 9684 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:26:14,280 basehttp 18560 9684 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:20,356 basehttp 18560 20680 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:20,381 basehttp 18560 13532 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:26:20,386 basehttp 18560 16236 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:26:20,396 basehttp 18560 14612 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:26:20,608 basehttp 18560 20680 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:26:20,782 basehttp 18560 9684 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:26:20,803 basehttp 18560 13532 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:20,809 basehttp 18560 9036 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:26:20,825 basehttp 18560 16236 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:26:20,897 basehttp 18560 14612 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:26:20,956 basehttp 18560 20680 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:26:21,110 basehttp 18560 16236 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:21,129 basehttp 18560 20680 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:26:21,182 basehttp 18560 13532 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:26:21,271 basehttp 18560 9036 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:26:21,277 basehttp 18560 9684 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:27:05,864 basehttp 18560 20992 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:27:05,904 basehttp 18560 18564 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:27:05,939 basehttp 18560 8108 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:27:05,945 basehttp 18560 19288 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:27:06,052 basehttp 18560 18564 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:27:06,144 basehttp 18560 8108 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:27:06,187 basehttp 18560 18564 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:27:06,210 basehttp 18560 18512 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:27:06,243 basehttp 18560 7000 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:27:06,252 basehttp 18560 19288 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:27:06,294 basehttp 18560 20992 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:27:06,689 basehttp 18560 18564 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:27:06,890 basehttp 18560 18512 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:27:06,943 basehttp 18560 7000 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:27:07,101 basehttp 18560 19288 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:27:07,228 basehttp 18560 18512 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:27:43,606 basehttp 18560 7000 "OPTIONS /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 0
INFO 2025-07-10 20:27:43,608 basehttp 18560 18512 "OPTIONS /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 0
INFO 2025-07-10 20:27:43,608 basehttp 18560 19288 "OPTIONS /api/contacts/customers/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 20:27:43,609 basehttp 18560 18564 "OPTIONS /api/contacts/customers/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 20:27:43,643 basehttp 18560 19288 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:27:43,643 basehttp 18560 18564 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:27:43,676 basehttp 18560 18512 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:27:43,692 basehttp 18560 18564 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:32:15,059 basehttp 18560 7000 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:32:15,147 basehttp 18560 18564 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:32:15,324 basehttp 18560 7000 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:15,334 basehttp 18560 18512 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:32:15,397 basehttp 18560 19288 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:32:15,511 basehttp 18560 13984 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:32:15,530 basehttp 18560 18564 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:32:15,600 basehttp 18560 7000 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:32:15,629 basehttp 18560 19288 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:15,701 basehttp 18560 18564 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:32:15,932 basehttp 18560 19288 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:15,983 basehttp 18560 7840 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:32:16,011 basehttp 18560 18512 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:32:16,053 basehttp 18560 13984 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:32:16,213 basehttp 18560 18512 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:16,474 basehttp 18560 19288 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:32:26,338 basehttp 18560 19288 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:32:26,350 basehttp 18560 18512 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:32:26,415 basehttp 18560 18512 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:32:26,418 basehttp 18560 13984 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:32:30,184 basehttp 18560 18512 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:32:30,185 basehttp 18560 13984 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:32:30,500 basehttp 18560 19288 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:32:30,507 basehttp 18560 13984 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:32:41,765 basehttp 18560 7000 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:41,766 basehttp 18560 7840 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:32:41,803 basehttp 18560 18564 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:32:41,834 basehttp 18560 19288 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:32:42,112 basehttp 18560 7840 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:42,112 basehttp 18560 18512 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:32:42,140 basehttp 18560 18564 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:32:42,166 basehttp 18560 13984 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:32:42,166 basehttp 18560 7000 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:32:42,223 basehttp 18560 19288 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:32:42,351 basehttp 18560 7840 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:32:42,486 basehttp 18560 18512 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:42,697 basehttp 18560 18564 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:32:43,012 basehttp 18560 18512 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:43,032 basehttp 18560 7000 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:32:43,035 basehttp 18560 13984 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:32:45,696 basehttp 18560 18564 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:32:45,735 basehttp 18560 7000 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:32:45,735 basehttp 18560 7840 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:45,780 basehttp 18560 19288 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:32:45,829 basehttp 18560 7840 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:32:45,850 basehttp 18560 7000 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:45,910 basehttp 18560 19288 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:32:45,973 basehttp 18560 13984 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:32:45,977 basehttp 18560 7840 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:32:46,120 basehttp 18560 18564 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:32:46,126 basehttp 18560 18512 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:32:46,188 basehttp 18560 13984 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:46,225 basehttp 18560 7840 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:32:46,308 basehttp 18560 18564 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:32:46,308 basehttp 18560 19288 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:32:46,366 basehttp 18560 18512 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:33:02,369 basehttp 18560 17404 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:33:02,392 basehttp 18560 19304 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:33:02,564 basehttp 18560 9076 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:33:02,570 basehttp 18560 19800 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:33:02,576 basehttp 18560 6996 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:33:02,627 basehttp 18560 17404 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:33:02,657 basehttp 18560 9076 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 20:33:02,712 basehttp 18560 19800 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:33:02,713 basehttp 18560 6996 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 20:33:02,721 basehttp 18560 17404 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 20:33:02,858 basehttp 18560 19800 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:33:03,007 basehttp 18560 11720 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:33:03,047 basehttp 18560 19304 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 20:33:03,144 basehttp 18560 9076 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 20:33:03,341 basehttp 18560 19800 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 20:33:04,080 basehttp 18560 19800 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 20:33:04,131 basehttp 18560 19800 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 20:33:04,173 basehttp 18560 19800 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 20:33:04,704 basehttp 18560 19800 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 20:35:56,403 basehttp 18560 19800 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 20:35:56,403 basehttp 18560 19800 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 20:36:04,850 basehttp 18560 19800 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:36:04,851 basehttp 18560 9076 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:36:04,878 basehttp 18560 19304 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 20:36:04,886 basehttp 18560 9076 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 20:36:12,330 basehttp 18560 9076 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 20:36:12,345 basehttp 18560 9076 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 20:45:09,088 autoreload 18560 20256 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:45:09,035 autoreload 10140 18164 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:45:13,467 autoreload 19228 19788 Watching for file changes with StatReloader
INFO 2025-07-10 20:45:13,677 autoreload 9212 18356 Watching for file changes with StatReloader
INFO 2025-07-10 20:49:17,068 autoreload 9212 18356 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:49:19,064 autoreload 1364 16600 Watching for file changes with StatReloader
INFO 2025-07-10 20:49:45,494 autoreload 1364 16600 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:49:48,180 autoreload 15476 17012 Watching for file changes with StatReloader
INFO 2025-07-10 20:52:04,306 autoreload 15476 17012 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:52:06,536 autoreload 5380 18148 Watching for file changes with StatReloader
INFO 2025-07-10 20:52:42,854 autoreload 5380 18148 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:52:44,934 autoreload 19772 8972 Watching for file changes with StatReloader
INFO 2025-07-10 20:54:13,300 autoreload 19772 8972 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:54:15,452 autoreload 14628 20788 Watching for file changes with StatReloader
INFO 2025-07-10 20:54:28,788 autoreload 14628 20788 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:54:30,824 autoreload 19728 8480 Watching for file changes with StatReloader
INFO 2025-07-10 20:54:44,905 autoreload 19728 8480 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:54:46,937 autoreload 19356 7108 Watching for file changes with StatReloader
INFO 2025-07-10 20:54:59,554 autoreload 19356 7108 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 20:55:01,919 autoreload 13708 13412 Watching for file changes with StatReloader
INFO 2025-07-10 20:56:07,455 autoreload 13708 13412 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\serializers.py changed, reloading.
INFO 2025-07-10 20:56:09,421 autoreload 9684 19312 Watching for file changes with StatReloader
INFO 2025-07-10 20:57:14,217 autoreload 9684 19312 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\serializers.py changed, reloading.
INFO 2025-07-10 20:57:16,066 autoreload 5332 7752 Watching for file changes with StatReloader
INFO 2025-07-10 20:57:38,595 autoreload 5332 7752 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\serializers.py changed, reloading.
INFO 2025-07-10 20:57:40,437 autoreload 18288 18860 Watching for file changes with StatReloader
INFO 2025-07-10 20:58:02,636 autoreload 18288 18860 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\serializers.py changed, reloading.
INFO 2025-07-10 20:58:04,379 autoreload 7908 16016 Watching for file changes with StatReloader
INFO 2025-07-10 20:58:28,442 autoreload 7908 16016 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\serializers.py changed, reloading.
INFO 2025-07-10 20:58:30,281 autoreload 18376 9496 Watching for file changes with StatReloader
INFO 2025-07-10 20:58:53,865 autoreload 18376 9496 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 20:58:56,077 autoreload 15456 12404 Watching for file changes with StatReloader
INFO 2025-07-10 20:59:35,612 autoreload 15456 12404 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 20:59:37,734 autoreload 17844 19848 Watching for file changes with StatReloader
INFO 2025-07-10 21:00:07,264 autoreload 17844 19848 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:00:08,747 autoreload 14340 7124 Watching for file changes with StatReloader
INFO 2025-07-10 21:00:36,006 autoreload 14340 7124 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:00:38,048 autoreload 17096 16164 Watching for file changes with StatReloader
INFO 2025-07-10 21:01:04,072 autoreload 17096 16164 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:01:05,780 autoreload 19472 18212 Watching for file changes with StatReloader
INFO 2025-07-10 21:01:34,072 autoreload 19472 18212 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\urls.py changed, reloading.
INFO 2025-07-10 21:01:35,677 autoreload 1128 6544 Watching for file changes with StatReloader
INFO 2025-07-10 21:01:55,227 autoreload 18860 18288 Watching for file changes with StatReloader
INFO 2025-07-10 21:02:31,838 basehttp 1128 21300 "GET /api/sales/sales-orders/ HTTP/1.1" 200 52
INFO 2025-07-10 21:02:32,372 basehttp 1128 21300 - Broken pipe from ('127.0.0.1', 55318)
INFO 2025-07-10 21:02:50,739 basehttp 1128 2848 "GET /api/sales/customer-invoices/ HTTP/1.1" 200 52
INFO 2025-07-10 21:02:51,192 basehttp 1128 2848 - Broken pipe from ('127.0.0.1', 55324)
INFO 2025-07-10 21:03:10,757 basehttp 1128 17148 "GET /api/sales/delivery-notes/ HTTP/1.1" 200 52
INFO 2025-07-10 21:03:11,220 basehttp 1128 17148 - Broken pipe from ('127.0.0.1', 55334)
WARNING 2025-07-10 21:06:11,422 log 1128 8228 Not Found: /api/Pricing/products/
WARNING 2025-07-10 21:06:11,422 basehttp 1128 8228 "GET /api/Pricing/products/ HTTP/1.1" 404 5782
INFO 2025-07-10 21:06:12,097 basehttp 1128 8228 - Broken pipe from ('127.0.0.1', 55382)
INFO 2025-07-10 21:06:55,914 basehttp 1128 15684 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:06:56,370 basehttp 1128 15684 - Broken pipe from ('127.0.0.1', 55400)
INFO 2025-07-10 21:07:12,808 basehttp 1128 17368 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:07:13,281 basehttp 1128 17368 - Broken pipe from ('127.0.0.1', 55406)
INFO 2025-07-10 21:07:28,930 basehttp 1128 8208 "GET /api/contacts/customers/ HTTP/1.1" 200 8470
INFO 2025-07-10 21:07:29,462 basehttp 1128 8208 - Broken pipe from ('127.0.0.1', 55414)
INFO 2025-07-10 21:07:56,844 basehttp 1128 13412 "POST /api/sales/sales-orders/ HTTP/1.1" 201 697
INFO 2025-07-10 21:07:59,777 basehttp 1128 13412 - Broken pipe from ('127.0.0.1', 55423)
INFO 2025-07-10 21:08:17,547 basehttp 1128 20392 "GET /api/sales/sales-orders/dashboard_stats/ HTTP/1.1" 200 113
INFO 2025-07-10 21:08:18,466 basehttp 1128 20392 - Broken pipe from ('127.0.0.1', 55435)
INFO 2025-07-10 21:08:40,637 basehttp 1128 18348 "GET /api/sales/customer-invoices/dashboard_stats/ HTTP/1.1" 200 141
INFO 2025-07-10 21:08:41,127 basehttp 1128 18348 - Broken pipe from ('127.0.0.1', 55446)
INFO 2025-07-10 21:09:15,595 autoreload 18860 18288 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 21:09:15,677 autoreload 1128 6544 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 21:09:18,912 autoreload 4852 17036 Watching for file changes with StatReloader
INFO 2025-07-10 21:09:18,922 autoreload 20420 20376 Watching for file changes with StatReloader
INFO 2025-07-10 21:09:44,687 autoreload 4852 17036 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 21:09:45,928 autoreload 20420 20376 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 21:09:47,707 autoreload 19900 21276 Watching for file changes with StatReloader
INFO 2025-07-10 21:09:48,590 autoreload 3612 21244 Watching for file changes with StatReloader
INFO 2025-07-10 21:10:01,964 autoreload 3612 21244 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 21:10:02,364 autoreload 19900 21276 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\models.py changed, reloading.
INFO 2025-07-10 21:10:05,377 autoreload 19216 17664 Watching for file changes with StatReloader
INFO 2025-07-10 21:10:05,537 autoreload 2188 19576 Watching for file changes with StatReloader
INFO 2025-07-10 21:10:33,160 autoreload 2188 19576 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:10:33,259 autoreload 19216 17664 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:10:35,523 autoreload 19636 17680 Watching for file changes with StatReloader
INFO 2025-07-10 21:10:35,610 autoreload 15744 15340 Watching for file changes with StatReloader
INFO 2025-07-10 21:10:55,384 autoreload 19636 17680 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:10:55,424 autoreload 15744 15340 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:10:58,865 autoreload 15480 16812 Watching for file changes with StatReloader
INFO 2025-07-10 21:10:58,887 autoreload 11964 19560 Watching for file changes with StatReloader
INFO 2025-07-10 21:11:22,748 autoreload 15480 16812 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:11:22,832 autoreload 11964 19560 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:11:26,229 autoreload 18156 9572 Watching for file changes with StatReloader
INFO 2025-07-10 21:11:26,265 autoreload 21060 15180 Watching for file changes with StatReloader
INFO 2025-07-10 21:11:40,563 basehttp 21060 18440 "GET /api/sales/sales-orders/available_products/ HTTP/1.1" 200 2
INFO 2025-07-10 21:11:45,122 basehttp 21060 18440 - Broken pipe from ('127.0.0.1', 55589)
INFO 2025-07-10 21:21:05,830 basehttp 21060 5068 "POST /api/sales/sales-orders/ HTTP/1.1" 201 704
INFO 2025-07-10 21:21:07,346 basehttp 21060 5068 - Broken pipe from ('127.0.0.1', 55825)
INFO 2025-07-10 21:22:21,056 basehttp 21060 18864 "GET /api/sales/sales-orders/ HTTP/1.1" 200 1454
INFO 2025-07-10 21:22:21,288 basehttp 21060 18864 - Broken pipe from ('127.0.0.1', 55859)
INFO 2025-07-10 21:22:38,876 basehttp 21060 8052 "GET /api/sales/sales-orders/available_products/ HTTP/1.1" 200 2
INFO 2025-07-10 21:22:38,945 basehttp 21060 8052 - Broken pipe from ('127.0.0.1', 55867)
INFO 2025-07-10 21:22:58,787 basehttp 21060 6908 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:22:58,882 basehttp 21060 6908 - Broken pipe from ('127.0.0.1', 55875)
INFO 2025-07-10 21:23:18,587 basehttp 21060 17512 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:23:18,755 basehttp 21060 17512 - Broken pipe from ('127.0.0.1', 55882)
INFO 2025-07-10 21:24:12,070 autoreload 21060 15180 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:24:12,147 autoreload 18156 9572 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:24:14,988 autoreload 15424 12964 Watching for file changes with StatReloader
INFO 2025-07-10 21:24:14,988 autoreload 18528 15688 Watching for file changes with StatReloader
INFO 2025-07-10 21:24:41,689 basehttp 15424 19852 "GET /api/sales/sales-orders/available_products/ HTTP/1.1" 200 2
INFO 2025-07-10 21:24:41,770 basehttp 15424 19852 - Broken pipe from ('127.0.0.1', 55923)
INFO 2025-07-10 21:26:11,267 autoreload 15424 12964 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:26:11,311 autoreload 18528 15688 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:26:13,289 autoreload 10328 19276 Watching for file changes with StatReloader
INFO 2025-07-10 21:26:13,289 autoreload 15420 17984 Watching for file changes with StatReloader
INFO 2025-07-10 21:26:47,472 autoreload 15420 17984 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:26:47,474 autoreload 10328 19276 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:26:50,345 autoreload 20064 16504 Watching for file changes with StatReloader
INFO 2025-07-10 21:26:50,370 autoreload 13544 19896 Watching for file changes with StatReloader
ERROR 2025-07-10 21:27:11,472 log 20064 8980 Internal Server Error: /api/sales/sales-orders/available_products/
ERROR 2025-07-10 21:27:11,473 basehttp 20064 8980 "GET /api/sales/sales-orders/available_products/ HTTP/1.1" 500 842
INFO 2025-07-10 21:27:12,077 basehttp 20064 8980 - Broken pipe from ('127.0.0.1', 56096)
INFO 2025-07-10 21:28:24,130 autoreload 13544 19896 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:28:24,152 autoreload 20064 16504 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:28:26,218 autoreload 18560 16860 Watching for file changes with StatReloader
INFO 2025-07-10 21:28:26,238 autoreload 18616 18316 Watching for file changes with StatReloader
INFO 2025-07-10 21:28:41,317 autoreload 18560 16860 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:28:41,371 autoreload 18616 18316 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:28:43,489 autoreload 14552 12656 Watching for file changes with StatReloader
INFO 2025-07-10 21:28:43,543 autoreload 7472 20956 Watching for file changes with StatReloader
INFO 2025-07-10 21:29:02,474 autoreload 7472 20956 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:29:02,574 autoreload 14552 12656 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\views.py changed, reloading.
INFO 2025-07-10 21:29:04,574 autoreload 12812 15576 Watching for file changes with StatReloader
INFO 2025-07-10 21:29:04,634 autoreload 19408 18552 Watching for file changes with StatReloader
INFO 2025-07-10 21:29:27,358 basehttp 12812 21268 "GET /api/sales/sales-orders/available_products/ HTTP/1.1" 200 4249
INFO 2025-07-10 21:29:27,548 basehttp 12812 21268 - Broken pipe from ('127.0.0.1', 56191)
INFO 2025-07-10 21:32:04,148 basehttp 12812 19932 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-10 21:32:04,330 basehttp 12812 19932 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4181
INFO 2025-07-10 21:32:06,910 basehttp 12812 19932 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-10 21:32:06,992 basehttp 12812 19932 "GET /admin/ HTTP/1.1" 200 38078
INFO 2025-07-10 21:34:28,979 autoreload 19408 18552 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\admin.py changed, reloading.
INFO 2025-07-10 21:34:28,980 autoreload 12812 15576 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\admin.py changed, reloading.
INFO 2025-07-10 21:34:31,825 autoreload 9736 9136 Watching for file changes with StatReloader
INFO 2025-07-10 21:34:31,833 autoreload 1108 19808 Watching for file changes with StatReloader
INFO 2025-07-10 21:35:14,231 autoreload 1108 19808 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\admin.py changed, reloading.
INFO 2025-07-10 21:35:14,244 autoreload 9736 9136 D:\erp_accv1\accounting_software\accounting_software\erp_backend\sales\admin.py changed, reloading.
INFO 2025-07-10 21:35:16,462 autoreload 19324 13448 Watching for file changes with StatReloader
INFO 2025-07-10 21:35:16,491 autoreload 18816 8064 Watching for file changes with StatReloader
INFO 2025-07-10 21:35:31,797 basehttp 19324 10952 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-10 21:35:32,029 basehttp 19324 10952 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4181
INFO 2025-07-10 21:35:32,139 basehttp 19324 6252 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-07-10 21:35:32,139 basehttp 19324 12528 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-07-10 21:35:32,194 basehttp 19324 10952 "GET /static/admin/css/base.css HTTP/1.1" 200 21310
INFO 2025-07-10 21:35:32,256 basehttp 19324 6252 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-07-10 21:35:32,256 basehttp 19324 12528 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
INFO 2025-07-10 21:35:32,443 basehttp 19324 10952 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-07-10 21:35:33,234 basehttp 19324 8480 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
WARNING 2025-07-10 21:35:33,496 log 19324 8480 Not Found: /favicon.ico
WARNING 2025-07-10 21:35:33,561 basehttp 19324 8480 "GET /favicon.ico HTTP/1.1" 404 3566
INFO 2025-07-10 21:35:36,505 basehttp 19324 8480 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-10 21:35:43,089 basehttp 19324 8480 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-10 21:35:43,375 basehttp 19324 8480 "GET /admin/ HTTP/1.1" 200 39958
INFO 2025-07-10 21:35:43,728 basehttp 19324 8480 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-10 21:35:43,759 basehttp 19324 10952 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-07-10 21:35:43,759 basehttp 19324 8480 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-10 21:35:43,783 basehttp 19324 12528 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
INFO 2025-07-10 21:41:47,338 basehttp 19324 20128 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:41:47,439 basehttp 19324 14548 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:41:47,497 basehttp 19324 16908 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:41:47,572 basehttp 19324 18156 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:47,594 basehttp 19324 14548 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:41:47,658 basehttp 19324 16316 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:41:47,686 basehttp 19324 20128 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:41:47,706 basehttp 19324 20428 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:41:47,801 basehttp 19324 14548 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:47,805 basehttp 19324 18156 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:41:47,962 basehttp 19324 16908 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:41:47,965 basehttp 19324 20128 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:41:48,064 basehttp 19324 16316 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:41:48,102 basehttp 19324 14548 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:48,427 basehttp 19324 20428 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:41:48,451 basehttp 19324 16316 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:50,645 basehttp 19324 18156 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:41:50,668 basehttp 19324 20128 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:50,668 basehttp 19324 16908 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:41:50,702 basehttp 19324 20428 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:41:50,921 basehttp 19324 16316 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:41:51,006 basehttp 19324 20128 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:41:51,023 basehttp 19324 16908 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:51,034 basehttp 19324 14548 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:41:51,056 basehttp 19324 20428 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:41:51,064 basehttp 19324 18156 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:41:51,086 basehttp 19324 16316 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:41:51,270 basehttp 19324 20128 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:41:51,411 basehttp 19324 20128 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:51,423 basehttp 19324 18156 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:41:51,447 basehttp 19324 16908 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:41:51,462 basehttp 19324 20128 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:41:57,338 basehttp 19324 20128 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:41:57,392 basehttp 19324 20128 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:41:58,785 basehttp 19324 20128 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 21:41:58,786 basehttp 19324 16908 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 21:41:58,827 basehttp 19324 18156 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 21:41:58,842 basehttp 19324 16908 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 21:42:02,514 basehttp 19324 16316 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:02,514 basehttp 19324 20428 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:42:02,532 basehttp 19324 14548 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:42:02,744 basehttp 19324 16316 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:42:02,762 basehttp 19324 16908 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:42:02,765 basehttp 19324 20428 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:42:02,815 basehttp 19324 18156 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:42:02,827 basehttp 19324 14548 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:02,830 basehttp 19324 20128 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:42:02,980 basehttp 19324 20428 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:42:03,056 basehttp 19324 16316 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:42:03,115 basehttp 19324 18156 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:42:03,210 basehttp 19324 16908 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:42:03,226 basehttp 19324 20128 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:03,305 basehttp 19324 14548 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:42:03,518 basehttp 19324 18156 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:42:03,591 basehttp 19324 20428 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:42:03,649 basehttp 19324 20128 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:19,046 basehttp 19324 16908 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:19,090 basehttp 19324 20428 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:42:19,136 basehttp 19324 16316 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:42:19,138 basehttp 19324 14548 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:42:19,334 basehttp 19324 20128 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:42:19,391 basehttp 19324 20428 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:19,434 basehttp 19324 16908 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:42:19,696 basehttp 19324 18156 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:42:19,710 basehttp 19324 16316 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:42:19,779 basehttp 19324 14548 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:42:19,794 basehttp 19324 20128 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:42:19,850 basehttp 19324 16908 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:19,951 basehttp 19324 20428 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:42:20,129 basehttp 19324 16316 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:42:20,155 basehttp 19324 16908 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:42:20,222 basehttp 19324 18156 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:44:25,553 basehttp 19324 18156 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:44:25,568 basehttp 19324 18156 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:44:30,720 basehttp 19324 18156 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 21:44:30,720 basehttp 19324 16316 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 21:44:30,756 basehttp 19324 16316 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 21:44:30,769 basehttp 19324 18156 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 21:44:51,298 basehttp 19324 18156 "OPTIONS /api/contacts/customers/ HTTP/1.1" 200 0
INFO 2025-07-10 21:44:51,351 basehttp 19324 18156 "POST /api/contacts/customers/ HTTP/1.1" 201 798
INFO 2025-07-10 21:44:52,929 basehttp 19324 18156 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 21:44:52,934 basehttp 19324 16316 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 21:44:56,552 basehttp 19324 16316 "OPTIONS /api/contacts/customers/?page=2&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 0
INFO 2025-07-10 21:44:56,581 basehttp 19324 16316 "GET /api/contacts/customers/?page=2&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8545
INFO 2025-07-10 21:45:04,433 basehttp 19324 16316 "OPTIONS /api/pricing/price-lists/ HTTP/1.1" 200 0
INFO 2025-07-10 21:45:04,437 basehttp 19324 18156 "OPTIONS /api/pricing/price-list-items/ HTTP/1.1" 200 0
INFO 2025-07-10 21:45:04,444 basehttp 19324 16908 "OPTIONS /api/pricing/price-lists/ HTTP/1.1" 200 0
INFO 2025-07-10 21:45:04,444 basehttp 19324 16316 "OPTIONS /api/pricing/price-list-items/ HTTP/1.1" 200 0
INFO 2025-07-10 21:45:04,475 basehttp 19324 14548 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:45:04,521 basehttp 19324 18156 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:45:04,548 basehttp 19324 14548 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:45:04,572 basehttp 19324 16316 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:45:04,575 basehttp 19324 20428 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:45:04,641 basehttp 19324 16316 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:45:43,974 basehttp 19324 16316 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:45:43,996 basehttp 19324 16316 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:47:14,374 basehttp 19324 16316 "OPTIONS /api/sales/products/?page_size=1000 HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,382 basehttp 19324 20428 "OPTIONS /api/purchase/vendors/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,382 basehttp 19324 14548 "OPTIONS /api/sales/categories/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,382 basehttp 19324 18156 "OPTIONS /api/sales/payment-terms/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,400 basehttp 19324 16316 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,403 basehttp 19324 20428 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,406 basehttp 19324 14548 "OPTIONS /api/account/company/current/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,411 basehttp 19324 18156 "OPTIONS /api/account/user/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,429 basehttp 19324 20428 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,484 basehttp 19324 16908 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:47:14,497 basehttp 19324 14548 "OPTIONS /api/sales/products/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:14,564 basehttp 19324 18156 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:47:14,597 basehttp 19324 20428 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:47:14,632 basehttp 19324 14548 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:47:14,691 basehttp 19324 18156 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 21:47:14,714 basehttp 19324 14548 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:47:14,729 basehttp 19324 20428 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 21:47:14,775 basehttp 19324 20128 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:47:14,779 basehttp 19324 14548 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:47:14,808 basehttp 19324 16316 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:47:14,826 basehttp 19324 18156 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 21:47:14,842 basehttp 19324 16908 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:47:14,946 basehttp 19324 18156 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:47:14,981 basehttp 19324 20428 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 16808
INFO 2025-07-10 21:47:15,033 basehttp 19324 16316 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:47:15,166 basehttp 19324 16316 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 21:47:22,323 basehttp 19324 20428 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:22,324 basehttp 19324 16316 "OPTIONS /api/gl/company-currency-info/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:22,334 basehttp 19324 20428 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:47:22,349 basehttp 19324 20428 "GET /api/gl/company-currency-info/ HTTP/1.1" 200 694
INFO 2025-07-10 21:47:29,022 basehttp 19324 16316 "OPTIONS /api/contacts/customers/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:29,022 basehttp 19324 20428 "OPTIONS /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 0
INFO 2025-07-10 21:47:29,022 basehttp 19324 16908 "OPTIONS /api/contacts/customers/stats/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:29,022 basehttp 19324 18156 "OPTIONS /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 0
INFO 2025-07-10 21:47:29,050 basehttp 19324 16908 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 21:47:29,064 basehttp 19324 18156 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 21:47:29,103 basehttp 19324 16316 "GET /api/contacts/customers/?page=1&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8512
INFO 2025-07-10 21:47:29,105 basehttp 19324 16908 "GET /api/contacts/customers/stats/ HTTP/1.1" 200 272
INFO 2025-07-10 21:47:31,840 basehttp 19324 16908 "OPTIONS /api/contacts/customers/?page=2&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 0
INFO 2025-07-10 21:47:31,867 basehttp 19324 16908 "GET /api/contacts/customers/?page=2&page_size=10&search=&ordering=-created_at HTTP/1.1" 200 8545
INFO 2025-07-10 21:47:44,349 basehttp 19324 16908 "OPTIONS /api/pricing/price-lists/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:44,350 basehttp 19324 20428 "OPTIONS /api/pricing/price-lists/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:44,350 basehttp 19324 16316 "OPTIONS /api/pricing/price-list-items/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:44,350 basehttp 19324 14548 "OPTIONS /api/pricing/price-list-items/ HTTP/1.1" 200 0
INFO 2025-07-10 21:47:44,391 basehttp 19324 16908 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:47:44,426 basehttp 19324 18156 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:47:44,451 basehttp 19324 14548 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:47:44,451 basehttp 19324 16908 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:47:44,510 basehttp 19324 16316 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:47:44,521 basehttp 19324 14548 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:48:17,287 basehttp 19324 16316 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:48:17,406 basehttp 19324 14548 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:48:51,162 basehttp 19324 14548 "POST /api/sales/products/ HTTP/1.1" 201 746
INFO 2025-07-10 21:48:53,080 basehttp 19324 14548 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:48:53,268 basehttp 19324 16316 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 21:49:19,095 basehttp 19324 16316 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:49:19,118 basehttp 19324 16908 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:49:19,128 basehttp 19324 16316 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:49:19,146 basehttp 19324 14548 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:49:19,175 basehttp 19324 16908 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:49:19,249 basehttp 19324 16316 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:54:12,372 basehttp 19324 16316 "OPTIONS /api/contacts/customers/ HTTP/1.1" 200 0
INFO 2025-07-10 21:54:12,374 basehttp 19324 14548 "OPTIONS /api/contacts/customers/ HTTP/1.1" 200 0
INFO 2025-07-10 21:54:12,417 basehttp 19324 14548 "GET /api/contacts/customers/ HTTP/1.1" 200 8470
INFO 2025-07-10 21:54:12,455 basehttp 19324 16316 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:54:12,467 basehttp 19324 14548 "GET /api/contacts/customers/ HTTP/1.1" 200 8470
INFO 2025-07-10 21:54:12,524 basehttp 19324 16908 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:54:13,167 basehttp 19324 14548 "OPTIONS /api/pricing/discount-rules/ HTTP/1.1" 200 0
INFO 2025-07-10 21:54:13,167 basehttp 19324 16908 "OPTIONS /api/pricing/discount-rules/ HTTP/1.1" 200 0
INFO 2025-07-10 21:54:13,190 basehttp 19324 14548 "GET /api/pricing/discount-rules/ HTTP/1.1" 200 476
INFO 2025-07-10 21:54:13,215 basehttp 19324 14548 "GET /api/pricing/discount-rules/ HTTP/1.1" 200 476
INFO 2025-07-10 21:54:14,313 basehttp 19324 14548 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:54:14,329 basehttp 19324 14548 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:54:18,653 basehttp 19324 14548 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:54:18,680 basehttp 19324 16316 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:54:18,752 basehttp 19324 16316 "GET /api/pricing/price-list-items/ HTTP/1.1" 200 1281
INFO 2025-07-10 21:54:18,754 basehttp 19324 14548 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:54:18,765 basehttp 19324 16908 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:54:18,915 basehttp 19324 16908 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 21:54:36,894 basehttp 19324 16908 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:54:36,925 basehttp 19324 16908 "GET /api/pricing/price-lists/ HTTP/1.1" 200 777
INFO 2025-07-10 21:55:28,417 basehttp 19324 7812 "GET /api/sales/sales-orders/available_products/ HTTP/1.1" 200 4440
INFO 2025-07-10 21:55:28,693 basehttp 19324 7812 - Broken pipe from ('127.0.0.1', 56800)
INFO 2025-07-10 23:06:30,815 basehttp 19324 15532 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-10 23:06:30,866 basehttp 19324 15532 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4181
INFO 2025-07-10 23:06:30,891 basehttp 19324 15532 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-07-10 23:06:33,105 basehttp 19324 15532 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-10 23:06:33,173 basehttp 19324 15532 "GET /admin/ HTTP/1.1" 200 39958
INFO 2025-07-10 23:06:33,205 basehttp 19324 15532 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-10 23:11:03,231 basehttp 19324 15532 "GET /admin/ HTTP/1.1" 200 39958
INFO 2025-07-10 23:11:05,484 basehttp 19324 15532 "GET /admin/ HTTP/1.1" 200 39958
INFO 2025-07-10 23:13:00,666 basehttp 19324 10328 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:00,744 basehttp 19324 17568 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 23:13:00,779 basehttp 19324 14140 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 23:13:00,834 basehttp 19324 19256 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 23:13:00,866 basehttp 19324 17568 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:00,908 basehttp 19324 17568 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 23:13:00,946 basehttp 19324 19256 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 23:13:00,947 basehttp 19324 14140 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 23:13:00,982 basehttp 19324 17568 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:00,988 basehttp 19324 10328 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 23:13:01,237 basehttp 19324 18304 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 23:13:01,262 basehttp 19324 18228 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 17555
INFO 2025-07-10 23:13:01,385 basehttp 19324 17568 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 23:13:01,533 basehttp 19324 18304 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 17555
INFO 2025-07-10 23:13:01,546 basehttp 19324 17568 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:01,551 basehttp 19324 10328 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 23:13:18,714 basehttp 19324 19256 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:18,744 basehttp 19324 14140 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 23:13:18,751 basehttp 19324 18228 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 23:13:18,766 basehttp 19324 17568 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 23:13:18,842 basehttp 19324 18228 "GET /api/account/user/ HTTP/1.1" 200 134
INFO 2025-07-10 23:13:18,888 basehttp 19324 14140 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:18,944 basehttp 19324 17568 "GET /api/purchase/vendors/ HTTP/1.1" 200 8215
INFO 2025-07-10 23:13:18,960 basehttp 19324 19256 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 23:13:19,022 basehttp 19324 14140 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:19,055 basehttp 19324 18228 "GET /api/sales/payment-terms/ HTTP/1.1" 200 2304
INFO 2025-07-10 23:13:19,122 basehttp 19324 10328 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 17555
INFO 2025-07-10 23:13:19,191 basehttp 19324 18304 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 23:13:19,544 basehttp 19324 18228 "GET /api/account/company/current/ HTTP/1.1" 200 655
INFO 2025-07-10 23:13:19,563 basehttp 19324 17568 "GET /api/sales/products/ HTTP/1.1" 200 7919
INFO 2025-07-10 23:13:19,606 basehttp 19324 14140 "GET /api/sales/products/?page_size=1000 HTTP/1.1" 200 17555
INFO 2025-07-10 23:13:19,698 basehttp 19324 18304 "GET /api/sales/categories/ HTTP/1.1" 200 5543
INFO 2025-07-10 23:13:21,576 basehttp 19324 15532 "GET /admin/ HTTP/1.1" 200 39958
