from rest_framework import serializers
from .models import (
    ProductCategory, Product, PaymentTerm, SalesOrder, SalesOrderLineItem,
    GoodsDeliveryNote, GoodsDeliveryNoteLineItem, CustomerInvoice, CustomerInvoiceLineItem,
    GoodsDeliveryReturnNote, GoodsDeliveryReturnNoteLineItem
)
from contacts.models import Contact
from django.db import models


class CustomerSerializer(serializers.ModelSerializer):
    """Serializer for Customer model (now using contacts.Contact)"""
    
    class Meta:
        model = Contact
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class ProductCategorySerializer(serializers.ModelSerializer):
    """Enhanced serializer for ProductCategory model"""
    
    # Computed fields
    products_count = serializers.ReadOnlyField()
    subcategories_count = serializers.ReadOnlyField()
    full_path = serializers.ReadOnlyField()
    
    # Related fields
    parent_category_name = serializers.CharField(source='parent_category.name', read_only=True)
    subcategories = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = [
            'id', 'name', 'code', 'description', 'parent_category', 'parent_category_name',
            'level', 'division_type', 'image_url', 'tax_category', 'margin_percentage',
            'is_active', 'sort_order', 'allow_subcategories', 'requires_expiry_tracking',
            'requires_batch_tracking', 'default_unit_of_measure', 'created_at', 'updated_at',
            'products_count', 'subcategories_count', 'full_path', 'subcategories'
        ]
        read_only_fields = ('created_at', 'updated_at', 'created_by', 'level')
    
    def get_subcategories(self, obj):
        """Get immediate subcategories"""
        if hasattr(obj, 'subcategories'):
            subcategories = obj.subcategories.filter(is_active=True).order_by('sort_order', 'name')
            return ProductCategorySerializer(subcategories, many=True, context=self.context).data
        return []
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate_code(self, value):
        """Validate category code is unique"""
        if self.instance:
            # For updates, exclude current instance
            if ProductCategory.objects.filter(code=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Category code must be unique.")
        else:
            # For creation
            if ProductCategory.objects.filter(code=value).exists():
                raise serializers.ValidationError("Category code must be unique.")
        return value.upper()
    
    def validate_parent_category(self, value):
        """Validate parent category to prevent circular references"""
        if value and self.instance:
            # Check if setting this parent would create a circular reference
            current_category = self.instance
            parent = value
            while parent:
                if parent.id == current_category.id:
                    raise serializers.ValidationError("Cannot set parent category that would create a circular reference.")
                parent = parent.parent_category
        return value
    
    def validate(self, data):
        """Additional validation"""
        # Check hierarchy level
        parent_category = data.get('parent_category')
        if parent_category and parent_category.level >= 3:
            raise serializers.ValidationError("Category hierarchy cannot exceed 3 levels.")
        
        return data


class ProductSerializer(serializers.ModelSerializer):
    """Serializer for Product model with GL Account integration"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    # GL Account details for display
    income_account_name = serializers.CharField(source='income_account_gl.account_name', read_only=True)
    income_account_number = serializers.CharField(source='income_account_gl.account_number', read_only=True)
    expense_account_name = serializers.CharField(source='expense_account_gl.account_name', read_only=True)
    expense_account_number = serializers.CharField(source='expense_account_gl.account_number', read_only=True)
    inventory_account_name = serializers.CharField(source='inventory_asset_account_gl.account_name', read_only=True)
    inventory_account_number = serializers.CharField(source='inventory_asset_account_gl.account_number', read_only=True)
    
    # Add total inventory quantity from all warehouses
    total_quantity_on_hand = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = ('product_id', 'created_at', 'updated_at', 'created_by', 'total_quantity_on_hand')
    
    def get_total_quantity_on_hand(self, obj):
        """Get total quantity from all warehouses"""
        from inventory.models import Inventory
        total_qty = Inventory.objects.filter(product=obj).aggregate(
            total=models.Sum('quantity_on_hand')
        )['total'] or 0
        return float(total_qty)
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)














class PaymentTermSerializer(serializers.ModelSerializer):
    """Serializer for Payment Term model"""
    
    class Meta:
        model = PaymentTerm
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)








# Enhanced Product Serializer with Sales Price Authority
class ProductPricingSerializer(serializers.ModelSerializer):
    """Special serializer for product pricing updates by Sales Department"""
    margin_amount = serializers.ReadOnlyField()
    margin_percentage = serializers.ReadOnlyField()
    markup_percentage = serializers.ReadOnlyField()
    current_average_cost = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'product_id', 'name', 'sku', 'unit_price', 'cost_price', 
            'minimum_selling_price', 'price_effective_date', 'price_last_updated_by',
            'price_last_updated_at', 'margin_amount', 'margin_percentage', 
            'markup_percentage', 'current_average_cost'
        ]
        read_only_fields = (
            'id', 'product_id', 'name', 'sku', 'cost_price', 'margin_amount', 
            'margin_percentage', 'markup_percentage', 'price_last_updated_by', 
            'price_last_updated_at', 'current_average_cost'
        )
    
    def get_current_average_cost(self, obj):
        """Get current weighted average cost"""
        return float(obj.get_current_average_cost())
    
    def validate_unit_price(self, value):
        """Validate sales price against minimum selling price"""
        if hasattr(self.instance, 'minimum_selling_price') and self.instance.minimum_selling_price:
            if value < self.instance.minimum_selling_price:
                raise serializers.ValidationError(
                    f"Sales price cannot be below minimum selling price of {self.instance.minimum_selling_price}"
                )
        return value
    
    def update(self, instance, validated_data):
        from django.utils import timezone
        # Track price updates
        if 'unit_price' in validated_data and instance.unit_price != validated_data['unit_price']:
            validated_data['price_last_updated_by'] = self.context['request'].user
            validated_data['price_last_updated_at'] = timezone.now()
            if not validated_data.get('price_effective_date'):
                validated_data['price_effective_date'] = timezone.now().date()
        
        return super().update(instance, validated_data)


# Sales Workflow Serializers

class SalesOrderLineItemSerializer(serializers.ModelSerializer):
    """Serializer for Sales Order Line Items"""

    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.code', read_only=True)

    class Meta:
        model = SalesOrderLineItem
        fields = [
            'id', 'product', 'product_name', 'product_code', 'description',
            'quantity', 'unit_of_measure', 'unit_price', 'discount_percent',
            'line_total', 'taxable', 'tax_rate', 'tax_amount',
            'quantity_delivered', 'quantity_pending', 'line_order'
        ]

    def validate(self, data):
        """Validate line item data"""
        if data.get('quantity', 0) <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0")
        if data.get('unit_price', 0) < 0:
            raise serializers.ValidationError("Unit price cannot be negative")
        return data


class SalesOrderSerializer(serializers.ModelSerializer):
    """Serializer for Sales Orders"""

    customer_name = serializers.CharField(source='customer.name', read_only=True)
    line_items = SalesOrderLineItemSerializer(many=True, read_only=True)

    class Meta:
        model = SalesOrder
        fields = [
            'id', 'so_id', 'so_number', 'customer', 'customer_name',
            'so_date', 'expected_date', 'seller_name', 'seller_email', 'seller_phone',
            'subtotal', 'discount_percent', 'discount_amount', 'tax_amount', 'total_amount',
            'amount_delivered', 'balance_due', 'status', 'payment_terms',
            'reference_number', 'memo', 'notes', 'ship_to_address',
            'email_sent', 'email_sent_date', 'line_items', 'created_at', 'updated_at'
        ]
        read_only_fields = ('so_id', 'so_number', 'created_at', 'updated_at')

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class GoodsDeliveryNoteLineItemSerializer(serializers.ModelSerializer):
    """Serializer for Goods Delivery Note Line Items"""

    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.code', read_only=True)

    class Meta:
        model = GoodsDeliveryNoteLineItem
        fields = [
            'id', 'sales_order_line_item', 'product', 'product_name', 'product_code',
            'description', 'quantity_ordered', 'quantity_delivered', 'quantity_remaining',
            'unit_of_measure', 'unit_price', 'line_order', 'notes'
        ]

    def validate(self, data):
        """Validate delivery line item data"""
        if data.get('quantity_delivered', 0) < 0:
            raise serializers.ValidationError("Quantity delivered cannot be negative")

        # Check if quantity delivered doesn't exceed quantity ordered
        sales_order_line_item = data.get('sales_order_line_item')
        if sales_order_line_item:
            quantity_delivered = data.get('quantity_delivered', 0)
            if quantity_delivered > sales_order_line_item.quantity:
                raise serializers.ValidationError("Quantity delivered cannot exceed quantity ordered")

        return data


class GoodsDeliveryNoteSerializer(serializers.ModelSerializer):
    """Serializer for Goods Delivery Notes"""

    customer_name = serializers.CharField(source='customer.name', read_only=True)
    sales_order_number = serializers.CharField(source='sales_order.so_number', read_only=True)
    line_items = GoodsDeliveryNoteLineItemSerializer(many=True, read_only=True)

    class Meta:
        model = GoodsDeliveryNote
        fields = [
            'id', 'gdn_id', 'gdn_number', 'sales_order', 'sales_order_number',
            'customer', 'customer_name', 'delivery_date', 'expected_delivery_date',
            'actual_delivery_date', 'delivery_address', 'delivery_contact_person',
            'delivery_contact_phone', 'vehicle_number', 'driver_name', 'driver_phone',
            'status', 'notes', 'internal_notes', 'customer_signature', 'received_by',
            'received_date', 'line_items', 'created_at', 'updated_at'
        ]
        read_only_fields = ('gdn_id', 'gdn_number', 'created_at', 'updated_at')

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class CustomerInvoiceLineItemSerializer(serializers.ModelSerializer):
    """Serializer for Customer Invoice Line Items"""

    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.code', read_only=True)

    class Meta:
        model = CustomerInvoiceLineItem
        fields = [
            'id', 'delivery_note_line_item', 'product', 'product_name', 'product_code',
            'description', 'quantity', 'unit_of_measure', 'unit_price', 'line_total',
            'discount_percent', 'discount_amount', 'tax_rate', 'tax_amount',
            'account_code', 'account_name', 'line_order', 'notes'
        ]

    def validate(self, data):
        """Validate invoice line item data"""
        if data.get('quantity', 0) <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0")
        if data.get('unit_price', 0) < 0:
            raise serializers.ValidationError("Unit price cannot be negative")
        return data


class CustomerInvoiceSerializer(serializers.ModelSerializer):
    """Serializer for Customer Invoices"""

    customer_name = serializers.CharField(source='customer.name', read_only=True)
    sales_order_number = serializers.CharField(source='sales_order.so_number', read_only=True)
    delivery_note_number = serializers.CharField(source='delivery_note.gdn_number', read_only=True)
    line_items = CustomerInvoiceLineItemSerializer(many=True, read_only=True)

    class Meta:
        model = CustomerInvoice
        fields = [
            'id', 'invoice_id', 'invoice_number', 'customer', 'customer_name',
            'sales_order', 'sales_order_number', 'delivery_note', 'delivery_note_number',
            'invoice_type', 'status', 'invoice_date', 'due_date', 'payment_date',
            'subtotal', 'discount_percent', 'discount_amount', 'tax_amount',
            'shipping_amount', 'total_amount', 'amount_paid', 'balance_due',
            'payment_terms', 'payment_method', 'reference_number', 'memo', 'notes',
            'billing_address', 'email_sent', 'email_sent_date', 'email_viewed',
            'email_viewed_date', 'line_items', 'created_at', 'updated_at'
        ]
        read_only_fields = ('invoice_id', 'invoice_number', 'balance_due', 'created_at', 'updated_at')

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class GoodsDeliveryReturnNoteLineItemSerializer(serializers.ModelSerializer):
    """Serializer for Goods Delivery Return Note Line Items"""

    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.code', read_only=True)

    class Meta:
        model = GoodsDeliveryReturnNoteLineItem
        fields = [
            'id', 'delivery_note_line_item', 'product', 'product_name', 'product_code',
            'description', 'quantity_delivered', 'quantity_returned', 'unit_of_measure',
            'unit_price', 'return_reason', 'condition', 'line_order', 'notes'
        ]

    def validate(self, data):
        """Validate return line item data"""
        if data.get('quantity_returned', 0) < 0:
            raise serializers.ValidationError("Quantity returned cannot be negative")

        # Check if quantity returned doesn't exceed quantity delivered
        delivery_note_line_item = data.get('delivery_note_line_item')
        if delivery_note_line_item:
            quantity_returned = data.get('quantity_returned', 0)
            if quantity_returned > delivery_note_line_item.quantity_delivered:
                raise serializers.ValidationError("Quantity returned cannot exceed quantity delivered")

        return data


class GoodsDeliveryReturnNoteSerializer(serializers.ModelSerializer):
    """Serializer for Goods Delivery Return Notes"""

    customer_name = serializers.CharField(source='customer.name', read_only=True)
    delivery_note_number = serializers.CharField(source='delivery_note.gdn_number', read_only=True)
    line_items = GoodsDeliveryReturnNoteLineItemSerializer(many=True, read_only=True)

    class Meta:
        model = GoodsDeliveryReturnNote
        fields = [
            'id', 'gdrn_id', 'gdrn_number', 'delivery_note', 'delivery_note_number',
            'customer', 'customer_name', 'return_date', 'expected_return_date',
            'actual_return_date', 'return_reason', 'return_address', 'return_contact_person',
            'return_contact_phone', 'status', 'notes', 'internal_notes',
            'quality_check_passed', 'quality_check_notes', 'quality_checked_by',
            'quality_check_date', 'line_items', 'created_at', 'updated_at'
        ]
        read_only_fields = ('gdrn_id', 'gdrn_number', 'created_at', 'updated_at')

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)