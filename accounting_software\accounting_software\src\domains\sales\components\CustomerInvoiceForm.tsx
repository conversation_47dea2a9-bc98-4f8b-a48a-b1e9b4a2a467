import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Autocomplete,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useCustomer } from '../../../contexts/CustomerContext';
import salesOrderService, { AvailableProduct } from '../../../services/salesOrder.service';
import customerInvoiceService from '../../../services/customerInvoice.service';

// Types
interface CustomerInvoiceFormData {
  customer: number | null;
  invoice_date: string;
  due_date: string;
  payment_terms: string;
  reference_number: string;
  notes: string;
  billing_address: string;
  line_items: InvoiceLineItem[];
}

interface InvoiceLineItem {
  id?: number;
  product: number | null;
  description: string;
  quantity: number;
  unit_price: number;
  discount_percent: number;
  tax_rate: number;
  line_total: number;
}

interface CustomerInvoiceFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CustomerInvoiceFormData, action: 'save' | 'save-close' | 'save-new') => Promise<void>;
  invoice?: any;
}

const validationSchema = Yup.object({
  customer: Yup.number().required('Customer is required'),
  invoice_date: Yup.string().required('Invoice date is required'),
  due_date: Yup.string().required('Due date is required'),
  payment_terms: Yup.string().required('Payment terms are required'),
});

const CustomerInvoiceForm: React.FC<CustomerInvoiceFormProps> = ({
  open,
  onClose,
  onSubmit,
  invoice,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableProducts, setAvailableProducts] = useState<AvailableProduct[]>([]);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const { customers } = useCustomer() || { customers: [] };

  const formik = useFormik<CustomerInvoiceFormData>({
    initialValues: {
      customer: invoice?.customer || null,
      invoice_date: invoice?.invoice_date || dayjs().format('YYYY-MM-DD'),
      due_date: invoice?.due_date || dayjs().add(30, 'day').format('YYYY-MM-DD'),
      payment_terms: invoice?.payment_terms || 'Net 30',
      reference_number: invoice?.reference_number || '',
      notes: invoice?.notes || '',
      billing_address: invoice?.billing_address || '',
      line_items: invoice?.line_items || [{
        product: null,
        description: '',
        quantity: 1,
        unit_price: 0,
        discount_percent: 0,
        tax_rate: 0,
        line_total: 0,
      }],
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);
      try {
        await onSubmit(values, 'save');
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    },
  });

  // Load available products
  useEffect(() => {
    const loadProducts = async () => {
      setLoadingProducts(true);
      try {
        const products = await salesOrderService.getAvailableProducts();
        setAvailableProducts(products);
      } catch (err) {
        console.error('Error loading products:', err);
        setError('Failed to load available products');
      } finally {
        setLoadingProducts(false);
      }
    };

    if (open) {
      loadProducts();
    }
  }, [open]);

  const handleSubmitWithAction = async (action: 'save' | 'save-close' | 'save-new') => {
    setLoading(true);
    setError(null);
    try {
      await onSubmit(formik.values, action);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const addLineItem = () => {
    const newLineItem: InvoiceLineItem = {
      product: null,
      description: '',
      quantity: 1,
      unit_price: 0,
      discount_percent: 0,
      tax_rate: 0,
      line_total: 0,
    };
    formik.setFieldValue('line_items', [...formik.values.line_items, newLineItem]);
  };

  const removeLineItem = (index: number) => {
    const newLineItems = formik.values.line_items.filter((_, i) => i !== index);
    formik.setFieldValue('line_items', newLineItems);
  };

  const updateLineItem = (index: number, field: keyof InvoiceLineItem, value: any) => {
    const newLineItems = [...formik.values.line_items];
    newLineItems[index] = { ...newLineItems[index], [field]: value };
    
    // Auto-fill product details when product is selected
    if (field === 'product' && value) {
      const selectedProduct = availableProducts.find(p => p.id === value);
      if (selectedProduct) {
        newLineItems[index].description = selectedProduct.name;
        newLineItems[index].unit_price = selectedProduct.unit_price;
      }
    }
    
    // Calculate line total
    const lineItem = newLineItems[index];
    const subtotal = lineItem.quantity * lineItem.unit_price;
    const discountAmount = subtotal * (lineItem.discount_percent / 100);
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * (lineItem.tax_rate / 100);
    newLineItems[index].line_total = taxableAmount + taxAmount;
    
    formik.setFieldValue('line_items', newLineItems);
  };

  const calculateTotals = () => {
    const subtotal = formik.values.line_items.reduce((sum, item) => {
      return sum + (item.quantity * item.unit_price);
    }, 0);
    
    const totalDiscount = formik.values.line_items.reduce((sum, item) => {
      return sum + ((item.quantity * item.unit_price) * (item.discount_percent / 100));
    }, 0);
    
    const totalTax = formik.values.line_items.reduce((sum, item) => {
      const taxableAmount = (item.quantity * item.unit_price) - ((item.quantity * item.unit_price) * (item.discount_percent / 100));
      return sum + (taxableAmount * (item.tax_rate / 100));
    }, 0);
    
    const total = subtotal - totalDiscount + totalTax;
    
    return { subtotal, totalDiscount, totalTax, total };
  };

  const totals = calculateTotals();

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogTitle>
          {invoice ? 'Edit Customer Invoice' : 'Create Customer Invoice'}
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          <Grid container spacing={3}>
            {/* Customer Information */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Customer *</InputLabel>
                <Select
                  name="customer"
                  value={formik.values.customer || ''}
                  onChange={formik.handleChange}
                  error={formik.touched.customer && Boolean(formik.errors.customer)}
                >
                  {customers.map((customer) => (
                    <MenuItem key={customer.id} value={customer.id}>
                      {customer.display_name || customer.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Invoice Date */}
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Invoice Date *"
                value={dayjs(formik.values.invoice_date)}
                onChange={(date) => formik.setFieldValue('invoice_date', date?.format('YYYY-MM-DD'))}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    margin: 'normal',
                    error: formik.touched.invoice_date && Boolean(formik.errors.invoice_date),
                    helperText: formik.touched.invoice_date && formik.errors.invoice_date,
                  },
                }}
              />
            </Grid>

            {/* Due Date */}
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Due Date *"
                value={dayjs(formik.values.due_date)}
                onChange={(date) => formik.setFieldValue('due_date', date?.format('YYYY-MM-DD'))}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    margin: 'normal',
                    error: formik.touched.due_date && Boolean(formik.errors.due_date),
                    helperText: formik.touched.due_date && formik.errors.due_date,
                  },
                }}
              />
            </Grid>

            {/* Payment Terms */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                margin="normal"
                name="payment_terms"
                label="Payment Terms *"
                value={formik.values.payment_terms}
                onChange={formik.handleChange}
                error={formik.touched.payment_terms && Boolean(formik.errors.payment_terms)}
                helperText={formik.touched.payment_terms && formik.errors.payment_terms}
              />
            </Grid>

            {/* Reference Number */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                margin="normal"
                name="reference_number"
                label="Reference Number"
                value={formik.values.reference_number}
                onChange={formik.handleChange}
              />
            </Grid>

            {/* Notes */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                margin="normal"
                name="notes"
                label="Notes"
                multiline
                rows={2}
                value={formik.values.notes}
                onChange={formik.handleChange}
              />
            </Grid>

            {/* Line Items */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">Invoice Items</Typography>
                    <Button
                      startIcon={<AddIcon />}
                      onClick={addLineItem}
                      variant="outlined"
                      size="small"
                    >
                      Add Item
                    </Button>
                  </Box>
                  
                  {loadingProducts ? (
                    <Box display="flex" justifyContent="center" p={2}>
                      <CircularProgress />
                      <Typography sx={{ ml: 2 }}>Loading products...</Typography>
                    </Box>
                  ) : (
                    <TableContainer component={Paper}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Product</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell>Qty</TableCell>
                            <TableCell>Unit Price</TableCell>
                            <TableCell>Discount %</TableCell>
                            <TableCell>Tax %</TableCell>
                            <TableCell>Total</TableCell>
                            <TableCell>Action</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {formik.values.line_items.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Autocomplete
                                  size="small"
                                  options={availableProducts}
                                  getOptionLabel={(option) => `${option.code} - ${option.name}`}
                                  value={availableProducts.find(p => p.id === item.product) || null}
                                  onChange={(_, value) => updateLineItem(index, 'product', value?.id || null)}
                                  renderInput={(params) => (
                                    <TextField {...params} placeholder="Select product" />
                                  )}
                                  sx={{ minWidth: 200 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  value={item.description}
                                  onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                                  placeholder="Description"
                                  sx={{ minWidth: 150 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  type="number"
                                  value={item.quantity}
                                  onChange={(e) => updateLineItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                                  sx={{ width: 80 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  type="number"
                                  value={item.unit_price}
                                  onChange={(e) => updateLineItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                                  sx={{ width: 100 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  type="number"
                                  value={item.discount_percent}
                                  onChange={(e) => updateLineItem(index, 'discount_percent', parseFloat(e.target.value) || 0)}
                                  sx={{ width: 80 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  type="number"
                                  value={item.tax_rate}
                                  onChange={(e) => updateLineItem(index, 'tax_rate', parseFloat(e.target.value) || 0)}
                                  sx={{ width: 80 }}
                                />
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  ${item.line_total.toFixed(2)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <IconButton
                                  size="small"
                                  onClick={() => removeLineItem(index)}
                                  disabled={formik.values.line_items.length === 1}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                  
                  {/* Totals */}
                  <Box mt={2} display="flex" justifyContent="flex-end">
                    <Box minWidth={300}>
                      <Grid container spacing={1}>
                        <Grid item xs={6}>
                          <Typography>Subtotal:</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography align="right">${totals.subtotal.toFixed(2)}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography>Discount:</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography align="right">-${totals.totalDiscount.toFixed(2)}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography>Tax:</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography align="right">${totals.totalTax.toFixed(2)}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="h6">Total:</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="h6" align="right">${totals.total.toFixed(2)}</Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            onClick={() => handleSubmitWithAction('save')}
            disabled={loading}
            variant="contained"
          >
            {loading ? <CircularProgress size={20} /> : 'Save'}
          </Button>
          <Button
            onClick={() => handleSubmitWithAction('save-close')}
            disabled={loading}
            variant="contained"
            color="primary"
          >
            Save & Close
          </Button>
          <Button
            onClick={() => handleSubmitWithAction('save-new')}
            disabled={loading}
            variant="outlined"
          >
            Save & New
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default CustomerInvoiceForm;
