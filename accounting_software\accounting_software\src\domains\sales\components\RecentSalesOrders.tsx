import React from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Visibility, GetApp } from '@mui/icons-material';

const RecentSalesOrders: React.FC = () => {
  const salesOrders = [
    {
      id: 'SO-001',
      customer: 'ABC Corporation',
      date: '2024-03-15',
      amount: 2250.00,
      status: 'Delivered',
    },
    {
      id: 'SO-002',
      customer: 'XYZ Industries',
      date: '2024-03-12',
      amount: 4450.75,
      status: 'Pending',
    },
    {
      id: 'SO-003',
      customer: 'Tech Solutions Ltd',
      date: '2024-03-10',
      amount: 6200.00,
      status: 'Acknowledged',
    },
    {
      id: 'SO-004',
      customer: 'Global Services Inc',
      date: '2024-03-08',
      amount: 1750.50,
      status: 'Sent',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      case 'acknowledged':
        return 'info';
      case 'sent':
        return 'primary';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>ID</TableCell>
            <TableCell>Customer</TableCell>
            <TableCell>Date</TableCell>
            <TableCell align="right">Amount</TableCell>
            <TableCell>Status</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {salesOrders.map((order) => (
            <TableRow key={order.id} hover>
              <TableCell>{order.id}</TableCell>
              <TableCell>{order.customer}</TableCell>
              <TableCell>{new Date(order.date).toLocaleDateString()}</TableCell>
              <TableCell align="right">
                ${order.amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </TableCell>
              <TableCell>
                <Chip
                  label={order.status}
                  color={getStatusColor(order.status) as any}
                  size="small"
                />
              </TableCell>
              <TableCell align="right">
                <Tooltip title="View">
                  <IconButton size="small">
                    <Visibility fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Download">
                  <IconButton size="small">
                    <GetApp fontSize="small" />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default RecentSalesOrders;
