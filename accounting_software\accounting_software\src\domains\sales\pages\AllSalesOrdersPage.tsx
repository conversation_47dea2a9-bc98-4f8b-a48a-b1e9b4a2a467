import React from 'react';
import { 
  Container, Typography, Paper, Box, Grid, 
  Card, CardContent, IconButton, Tooltip 
} from '@mui/material';
import { 
  TrendingUp, AttachMoney, Receipt, 
  ShowChart, Download as DownloadIcon 
} from '@mui/icons-material';
import StatCard from '../../../shared/components/StatCard';
import RecentSalesOrders from '../components/RecentSalesOrders';
import SalesOrderChart from '../components/SalesOrderChart';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';

const AllSalesOrdersPage: React.FC = () => {
  // Mock data for analytics
  const salesStats = {
    totalSales: 287000,
    monthlyGrowth: 15.2,
    averageOrderValue: 1850,
    pendingOrders: 12,
  };

  const monthlyData = [
    { month: 'Jan', sales: 22200 },
    { month: 'Feb', sales: 24800 },
    { month: 'Mar', sales: 26200 },
    { month: 'Apr', sales: 28500 },
    { month: 'May', sales: 31700 },
    { month: 'Jun', sales: 33300 },
  ];

  return (
    <PageContainer title="Sales Overview">
      <Grid container spacing={3}>
        {/* Stats Cards */}
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Total Sales"
            value={`$${salesStats.totalSales.toLocaleString()}`}
            icon={<AttachMoney color="primary" />}
            change={salesStats.monthlyGrowth}
            changeText="vs last month"
            changeDirection="up"
          />
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Average Order Value"
            value={`$${salesStats.averageOrderValue.toLocaleString()}`}
            icon={<TrendingUp color="primary" />}
            change={4.8}
            changeText="vs last month"
            changeDirection="up"
          />
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Pending Orders"
            value={salesStats.pendingOrders.toString()}
            icon={<Receipt color="primary" />}
            change={3}
            changeText="vs last month"
            changeDirection="up"
          />
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Customer Satisfaction"
            value="96%"
            icon={<ShowChart color="primary" />}
            change={2.1}
            changeText="vs last month"
            changeDirection="up"
          />
        </Grid>
        
        {/* Charts */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">Sales Trends</Typography>
              <Tooltip title="Download Report">
                <IconButton size="small">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
            <SalesOrderChart data={monthlyData} />
          </Paper>
        </Grid>
        
        {/* Recent Sales Orders */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">Recent Sales Orders</Typography>
              <Tooltip title="View All">
                <IconButton size="small" onClick={() => window.location.href = '/dashboard/sales/orders'}>
                  <ShowChart />
                </IconButton>
              </Tooltip>
            </Box>
            <RecentSalesOrders />
          </Paper>
        </Grid>
      </Grid>
    </PageContainer>
  );
};

export default AllSalesOrdersPage;
