import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Grid,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  GetApp as DownloadIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';
import dayjs from 'dayjs';

// Types
interface CustomerBill {
  id: number;
  bill_number: string;
  customer: number;
  customer_name: string;
  bill_date: string;
  due_date: string;
  status: string;
  bill_type: string;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  source_type: string;
}

interface CustomerBillStats {
  total_bills: number;
  total_receivables: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

const CustomerBillsPage: React.FC = () => {
  const navigate = useNavigate();
  
  // State
  const [bills, setBills] = useState<CustomerBill[]>([]);
  const [stats, setStats] = useState<CustomerBillStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data
  const mockBills: CustomerBill[] = [
    {
      id: 1,
      bill_number: 'CBILL-000001',
      customer: 1,
      customer_name: 'ABC Corporation',
      bill_date: '2024-03-15',
      due_date: '2024-04-15',
      status: 'posted',
      bill_type: 'bill',
      subtotal: 2000.00,
      tax_amount: 200.00,
      total_amount: 2200.00,
      amount_paid: 0.00,
      balance_due: 2200.00,
      source_type: 'manual',
    },
    {
      id: 2,
      bill_number: 'CBILL-000002',
      customer: 2,
      customer_name: 'XYZ Industries',
      bill_date: '2024-03-12',
      due_date: '2024-04-12',
      status: 'paid',
      bill_type: 'bill',
      subtotal: 3500.00,
      tax_amount: 350.00,
      total_amount: 3850.00,
      amount_paid: 3850.00,
      balance_due: 0.00,
      source_type: 'so',
    },
  ];

  const mockStats: CustomerBillStats = {
    total_bills: 2,
    total_receivables: 6050.00,
    outstanding_amount: 2200.00,
    overdue_count: 0,
    draft_count: 0,
    paid_count: 1,
  };

  // Load data
  useEffect(() => {
    loadCustomerBills();
  }, []);

  const loadCustomerBills = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with actual API calls
      // For now, use mock data
      setBills(mockBills);
      setStats(mockStats);

    } catch (err) {
      console.error('Failed to load customer bills:', err);
      setError(err instanceof Error ? err.message : 'Failed to load customer bills');
      setBills([]);
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status.toLowerCase()) {
      case 'draft': return 'default';
      case 'posted': return 'primary';
      case 'sent': return 'info';
      case 'paid': return 'success';
      case 'overdue': return 'error';
      case 'cancelled': return 'secondary';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'draft': return 'Draft';
      case 'posted': return 'Posted';
      case 'sent': return 'Sent';
      case 'paid': return 'Paid';
      case 'overdue': return 'Overdue';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert 
          severity="error" 
          action={
            <Button color="inherit" size="small" onClick={loadCustomerBills}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Customer Bills
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => alert('Create Bill functionality will be implemented')}
        >
          Create Bill
        </Button>
      </Box>

      {/* Stats Cards */}
      {stats && (
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Bills"
              value={stats.total_bills.toString()}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Receivables"
              value={formatCurrency(stats.total_receivables)}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Outstanding"
              value={formatCurrency(stats.outstanding_amount)}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Paid Bills"
              value={stats.paid_count.toString()}
              color="info"
            />
          </Grid>
        </Grid>
      )}

      {/* Bills Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Customer Bills
          </Typography>
          
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Bill Number</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell align="right">Balance Due</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bills.map((bill) => (
                  <TableRow key={bill.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {bill.bill_number}
                      </Typography>
                    </TableCell>
                    <TableCell>{bill.customer_name}</TableCell>
                    <TableCell>
                      {dayjs(bill.bill_date).format('MMM DD, YYYY')}
                    </TableCell>
                    <TableCell>
                      {dayjs(bill.due_date).format('MMM DD, YYYY')}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(bill.status)}
                        color={getStatusColor(bill.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(bill.total_amount)}
                    </TableCell>
                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        color={bill.balance_due > 0 ? 'error' : 'success'}
                        fontWeight="medium"
                      >
                        {formatCurrency(bill.balance_due)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={() => alert('View functionality will be implemented')}
                      >
                        <ViewIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => alert('Edit functionality will be implemented')}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => alert('Download functionality will be implemented')}
                      >
                        <DownloadIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CustomerBillsPage;
