import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  TextField,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Divider,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Email as EmailIcon,
  FileCopy as DuplicateIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { StandardDatePicker } from '../../../shared/components';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';
import CustomerInvoiceForm from '../components/CustomerInvoiceForm';
import customerInvoiceService from '../../../services/customerInvoice.service';
import dayjs from 'dayjs';

// Mock interfaces for now
interface CustomerBill {
  id: number;
  bill_number: string;
  customer: number;
  customer_name?: string;
  bill_date: string;
  due_date: string;
  status: 'draft' | 'posted' | 'paid';
  bill_type?: 'bill' | 'credit';
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  source_type?: 'manual' | 'so' | 'delivery_note';
  created_at?: string;
  updated_at?: string;
}

interface CustomerBillStats {
  total_bills: number;
  total_receivables: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

interface CustomerBillFilters {
  status?: string;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

const CustomerBillsPage: React.FC = () => {
  const navigate = useNavigate();
  
  // State
  const [bills, setBills] = useState<CustomerBill[]>([]);
  const [stats, setStats] = useState<CustomerBillStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<CustomerBillFilters>({});
  const [formOpen, setFormOpen] = useState(false);
  const [selectedBill, setSelectedBill] = useState<CustomerBill | null>(null);

  // Load data from backend
  useEffect(() => {
    loadBills();
    loadStats();
  }, [filters]);

  const loadBills = async () => {
    try {
      setLoading(true);
      const response = await customerInvoiceService.getAll(filters);
      setBills(response.results || []);
    } catch (err) {
      setError('Failed to load customer invoices');
      console.error('Error loading bills:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await customerInvoiceService.getDashboardStats();
      setStats({
        total_bills: statsData.total_invoices,
        total_receivables: statsData.total_revenue,
        outstanding_amount: statsData.outstanding_amount,
        overdue_count: statsData.overdue_invoices,
        draft_count: statsData.draft_invoices,
        paid_count: statsData.paid_invoices,
      });
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  const handleFormSubmit = async (data: any, action: 'save' | 'save-close' | 'save-new') => {
    try {
      if (selectedBill) {
        await customerInvoiceService.update(selectedBill.id, data);
      } else {
        await customerInvoiceService.create(data);
      }

      await loadBills();
      await loadStats();

      if (action === 'save-close') {
        setFormOpen(false);
        setSelectedBill(null);
      } else if (action === 'save-new') {
        setSelectedBill(null);
        // Keep form open for new entry
      }
    } catch (err) {
      throw new Error('Failed to save customer invoice');
    }
  };

  // Mock data for fallback (remove this once backend is fully integrated)
  const mockBills: CustomerBill[] = bills.length === 0 ? [
    {
      id: 1,
      bill_number: 'CBILL-000001',
      customer: 1,
      customer_name: 'ABC Corporation',
      bill_date: '2024-03-15',
      due_date: '2024-04-15',
      status: 'posted',
      bill_type: 'bill',
      subtotal: 2000.00,
      tax_amount: 200.00,
      total_amount: 2200.00,
      amount_paid: 0.00,
      balance_due: 2200.00,
      source_type: 'manual',
    },
    {
      id: 2,
      bill_number: 'CBILL-000002',
      customer: 2,
      customer_name: 'XYZ Industries',
      bill_date: '2024-03-12',
      due_date: '2024-04-12',
      status: 'paid',
      bill_type: 'bill',
      subtotal: 3500.00,
      tax_amount: 350.00,
      total_amount: 3850.00,
      amount_paid: 3850.00,
      balance_due: 0.00,
      source_type: 'so',
    },
  ] : bills;

  const mockStats: CustomerBillStats = {
    total_bills: 2,
    total_receivables: 6050.00,
    outstanding_amount: 2200.00,
    overdue_count: 0,
    draft_count: 0,
    paid_count: 1,
  };

  // Load customer bills - using the new backend integration
  // (The loadBills and loadStats functions are already defined above)

  const handleFilterChange = (field: keyof CustomerBillFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this customer bill?')) {
      // TODO: Implement delete functionality
      console.log('Delete bill:', id);
    }
  };

  const handleDuplicate = async (id: number) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate bill:', id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'default';
      case 'posted':
        return 'warning';
      case 'paid':
        return 'success';
      default:
        return 'default';
    }
  };

  const getSourceIcon = (sourceType?: string) => {
    switch (sourceType) {
      case 'so':
        return <AssignmentIcon fontSize="small" />;
      case 'delivery_note':
        return <ReceiptIcon fontSize="small" />;
      default:
        return <DescriptionIcon fontSize="small" />;
    }
  };

  const getSourceLabel = (sourceType?: string) => {
    switch (sourceType) {
      case 'so':
        return 'Sales Order';
      case 'delivery_note':
        return 'Delivery Note';
      default:
        return 'Manual';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            Customer Bills
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            Manage accounts receivable and customer bills
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<ReceiptIcon />}
            onClick={() => navigate('/dashboard/sales/customer-bills/create-from-so')}
            sx={{ borderRadius: '8px' }}
          >
            From Sales Order
          </Button>
          <Button
            variant="outlined"
            startIcon={<DescriptionIcon />}
            onClick={() => navigate('/dashboard/sales/customer-bills/create-from-delivery')}
            sx={{ borderRadius: '8px' }}
          >
            From Delivery
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setSelectedBill(null);
              setFormOpen(true);
            }}
            sx={{ borderRadius: '8px' }}
          >
            Create Bill
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
          <Button
            size="small"
            onClick={() => {
              loadBills();
              loadStats();
            }}
            sx={{ ml: 2 }}
          >
            Retry
          </Button>
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mx: 'auto', mb: 1 }}>
                  <ReceiptIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.total_bills}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total Bills
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mx: 'auto', mb: 1 }}>
                  <AssignmentIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {formatCurrency(stats.total_receivables)}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total Receivables
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mx: 'auto', mb: 1 }}>
                  <ReceiptIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {formatCurrency(stats.outstanding_amount)}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Outstanding
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mx: 'auto', mb: 1 }}>
                  <ReceiptIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.overdue_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Overdue
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', 
              color: '#333',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(0,0,0,0.1)', mx: 'auto', mb: 1 }}>
                  <ReceiptIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.draft_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Draft
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)', 
              color: '#333',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(0,0,0,0.1)', mx: 'auto', mb: 1 }}>
                  <ReceiptIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.paid_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Paid
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Customer Bills Table */}
      <Card sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Bill Number</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Customer</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Due Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Source</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600, color: '#495057' }}>Amount</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600, color: '#495057' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bills.map((bill) => (
                  <TableRow key={bill.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {bill.bill_number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {bill.customer_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dayjs(bill.bill_date).format('DD/MM/YYYY')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dayjs(bill.due_date).format('DD/MM/YYYY')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={bill.status.toUpperCase()}
                        color={getStatusColor(bill.status) as any}
                        size="small"
                        sx={{ borderRadius: '6px' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getSourceIcon(bill.source_type)}
                        <Typography variant="caption">
                          {getSourceLabel(bill.source_type)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(bill.total_amount)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="View">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/dashboard/sales/customer-bills/${bill.id}`)}
                          >
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            onClick={() => {
                              setSelectedBill(bill);
                              setFormOpen(true);
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Duplicate">
                          <IconButton
                            size="small"
                            onClick={() => handleDuplicate(bill.id)}
                          >
                            <DuplicateIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(bill.id)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Customer Invoice Form */}
      <CustomerInvoiceForm
        open={formOpen}
        onClose={() => {
          setFormOpen(false);
          setSelectedBill(null);
        }}
        onSubmit={handleFormSubmit}
        invoice={selectedBill}
      />
    </Box>
  );
};

export default CustomerBillsPage;
