import api from './api';

// Customer Bill Types
export interface CustomerBillCustomer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
  payment_terms?: string;
}

export interface CustomerBillItem {
  id?: number;
  product?: number;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  taxable?: boolean;
  tax_rate?: number;
  tax_amount?: number;
  account_code?: string;
  line_order?: number;
}

export interface CustomerBill {
  id?: number;
  bill_id: string;
  bill_number: string;
  customer: number;
  customer_details?: CustomerBillCustomer;
  customer_name?: string; // Computed field for display
  bill_type: 'bill' | 'credit';
  
  // Dates
  bill_date: string;
  due_date: string;
  
  // Related Documents
  sales_order?: number;
  sales_order_number?: string;
  
  // Financial Information
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  
  // Status and Terms
  status: 'draft' | 'posted' | 'paid';
  payment_terms?: string;
  
  // Additional Information
  reference_number?: string;
  notes?: string;
  
  // Line Items
  line_items: CustomerBillItem[];
  
  // Metadata
  created_at?: string;
  updated_at?: string;
}

export interface CustomerBillFormData {
  customer: number;
  bill_type: 'bill' | 'credit';
  bill_date: string;
  due_date: string;
  sales_order?: number;
  status: 'draft' | 'posted';
  payment_terms?: string;
  reference_number?: string;
  notes?: string;
  line_items: CustomerBillItem[];
}

export interface CustomerBillFilters {
  status?: string;
  bill_type?: string;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CustomerBillStats {
  total_bills: number;
  total_receivables: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

export interface CreateBillFromSOData {
  sales_order: number;
  bill_date: string;
  due_date?: string;
  payment_terms?: string;
  notes?: string;
  line_items?: {
    sales_order_line_item: number;
    quantity?: number;
    unit_price?: number;
  }[];
}

class CustomerBillService {
  private baseUrl = '/sales';

  async getCustomerBills(filters?: CustomerBillFilters): Promise<{ results: CustomerBill[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.bill_type) params.append('bill_type', filters.bill_type);
    if (filters?.customer) params.append('customer', filters.customer.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/customer-bills/?${params.toString()}`);
    
    // Map customer details to customer_name for display
    const customerBills = response.data.results?.map((bill: CustomerBill) => ({
      ...bill,
      customer_name: bill.customer_details?.display_name || `Customer ${bill.customer}`
    })) || [];

    return {
      results: customerBills,
      count: response.data.count || 0
    };
  }

  async getCustomerBill(id: number): Promise<CustomerBill> {
    const response = await api.get(`${this.baseUrl}/customer-bills/${id}/`);
    
    // Map customer details to customer_name for display
    return {
      ...response.data,
      customer_name: response.data.customer_details?.display_name || `Customer ${response.data.customer}`
    };
  }

  async createCustomerBill(data: CustomerBillFormData): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/`, data);
    return response.data;
  }

  async updateCustomerBill(id: number, data: Partial<CustomerBillFormData>): Promise<CustomerBill> {
    const response = await api.patch(`${this.baseUrl}/customer-bills/${id}/`, data);
    return response.data;
  }

  async deleteCustomerBill(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/customer-bills/${id}/`);
  }

  async getCustomerBillStats(): Promise<CustomerBillStats> {
    const response = await api.get(`${this.baseUrl}/customer-bills/stats/`);
    return response.data;
  }

  async duplicateCustomerBill(id: number): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/${id}/duplicate/`);
    return response.data;
  }

  async postCustomerBill(id: number): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/${id}/post/`);
    return response.data;
  }

  async markAsPaid(id: number, paymentData?: { payment_date?: string; payment_method?: string; amount?: number }): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/${id}/mark_paid/`, paymentData);
    return response.data;
  }

  // Create bill from Sales Order
  async createBillFromSalesOrder(data: CreateBillFromSOData): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/create-from-so/`, data);
    return response.data;
  }

  // Get sales orders that can be billed
  async getBillableSalesOrders(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/sales-orders/billable/`);
    return response.data.results || [];
  }

  // Create credit bill (customer credit)
  async createCreditBill(data: Omit<CustomerBillFormData, 'bill_type'> & { original_bill?: number }): Promise<CustomerBill> {
    const creditData = {
      ...data,
      bill_type: 'credit' as const
    };
    const response = await api.post(`${this.baseUrl}/customer-bills/`, creditData);
    return response.data;
  }

  // Get overdue bills
  async getOverdueBills(): Promise<CustomerBill[]> {
    const response = await api.get(`${this.baseUrl}/customer-bills/overdue/`);
    return response.data.results || [];
  }

  // Send bill via email
  async sendBill(id: number, emailData?: { to: string; subject?: string; message?: string }): Promise<void> {
    await api.post(`${this.baseUrl}/customer-bills/${id}/send/`, emailData);
  }

  // Generate PDF
  async generatePDF(id: number): Promise<Blob> {
    const response = await api.get(`${this.baseUrl}/customer-bills/${id}/pdf/`, {
      responseType: 'blob'
    });
    return response.data;
  }
}

export const customerBillService = new CustomerBillService();
