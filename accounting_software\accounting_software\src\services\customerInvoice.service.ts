import { apiClient } from './api';

export interface CustomerInvoice {
  id?: number;
  invoice_id?: string;
  invoice_number?: string;
  customer: number;
  customer_name?: string;
  sales_order?: number;
  sales_order_number?: string;
  delivery_note?: number;
  delivery_note_number?: string;
  invoice_type: 'invoice' | 'credit';
  status: 'draft' | 'sent' | 'viewed' | 'partial' | 'paid' | 'overdue' | 'cancelled';
  invoice_date: string;
  due_date: string;
  payment_date?: string;
  subtotal: number;
  discount_percent: number;
  discount_amount: number;
  tax_amount: number;
  shipping_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  payment_terms?: string;
  payment_method?: string;
  reference_number?: string;
  memo?: string;
  notes?: string;
  billing_address?: string;
  email_sent: boolean;
  email_sent_date?: string;
  email_viewed: boolean;
  email_viewed_date?: string;
  line_items?: CustomerInvoiceLineItem[];
  created_at?: string;
  updated_at?: string;
}

export interface CustomerInvoiceLineItem {
  id?: number;
  delivery_note_line_item?: number;
  product?: number;
  product_name?: string;
  product_code?: string;
  description: string;
  quantity: number;
  unit_of_measure: string;
  unit_price: number;
  line_total: number;
  discount_percent: number;
  discount_amount: number;
  tax_rate: number;
  tax_amount: number;
  account_code?: string;
  account_name?: string;
  line_order: number;
  notes?: string;
}

export interface CustomerInvoiceStats {
  total_invoices: number;
  draft_invoices: number;
  sent_invoices: number;
  paid_invoices: number;
  overdue_invoices: number;
  total_revenue: number;
  outstanding_amount: number;
}

class CustomerInvoiceService {
  private baseUrl = '/api/sales/customer-invoices';

  async getAll(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    status?: string;
    invoice_type?: string;
    customer?: number;
    start_date?: string;
    end_date?: string;
  }) {
    const response = await apiClient.get(this.baseUrl, { params });
    return response.data;
  }

  async getById(id: number) {
    const response = await apiClient.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async create(invoice: Partial<CustomerInvoice>) {
    const response = await apiClient.post(this.baseUrl, invoice);
    return response.data;
  }

  async update(id: number, invoice: Partial<CustomerInvoice>) {
    const response = await apiClient.put(`${this.baseUrl}/${id}/`, invoice);
    return response.data;
  }

  async delete(id: number) {
    const response = await apiClient.delete(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async getDashboardStats(): Promise<CustomerInvoiceStats> {
    const response = await apiClient.get(`${this.baseUrl}/dashboard_stats/`);
    return response.data;
  }

  async markAsSent(id: number) {
    const response = await apiClient.post(`${this.baseUrl}/${id}/mark_as_sent/`);
    return response.data;
  }

  async recordPayment(id: number, data: {
    payment_amount: number;
    payment_date?: string;
    payment_method?: string;
  }) {
    const response = await apiClient.post(`${this.baseUrl}/${id}/record_payment/`, data);
    return response.data;
  }

  // Line Items
  async addLineItem(invoiceId: number, lineItem: Partial<CustomerInvoiceLineItem>) {
    const response = await apiClient.post(`${this.baseUrl}/${invoiceId}/line_items/`, lineItem);
    return response.data;
  }

  async updateLineItem(invoiceId: number, lineItemId: number, lineItem: Partial<CustomerInvoiceLineItem>) {
    const response = await apiClient.put(`${this.baseUrl}/${invoiceId}/line_items/${lineItemId}/`, lineItem);
    return response.data;
  }

  async deleteLineItem(invoiceId: number, lineItemId: number) {
    const response = await apiClient.delete(`${this.baseUrl}/${invoiceId}/line_items/${lineItemId}/`);
    return response.data;
  }

  // Utility methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  }

  getStatusColor(status: string): string {
    const statusColors: { [key: string]: string } = {
      draft: '#9e9e9e',
      sent: '#2196f3',
      viewed: '#4caf50',
      partial: '#ff9800',
      paid: '#8bc34a',
      overdue: '#f44336',
      cancelled: '#607d8b',
    };
    return statusColors[status] || '#9e9e9e';
  }

  getStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      draft: 'Draft',
      sent: 'Sent',
      viewed: 'Viewed',
      partial: 'Partially Paid',
      paid: 'Paid',
      overdue: 'Overdue',
      cancelled: 'Cancelled',
    };
    return statusLabels[status] || status;
  }

  getTypeLabel(type: string): string {
    const typeLabels: { [key: string]: string } = {
      invoice: 'Invoice',
      credit: 'Credit Note',
    };
    return typeLabels[type] || type;
  }

  calculateLineTotal(quantity: number, unitPrice: number, discountPercent: number = 0): number {
    const subtotal = quantity * unitPrice;
    const discountAmount = subtotal * (discountPercent / 100);
    return subtotal - discountAmount;
  }

  calculateTaxAmount(lineTotal: number, taxRate: number): number {
    return lineTotal * (taxRate / 100);
  }

  isOverdue(invoice: CustomerInvoice): boolean {
    if (invoice.status === 'paid' || invoice.status === 'cancelled') {
      return false;
    }
    const dueDate = new Date(invoice.due_date);
    const today = new Date();
    return dueDate < today;
  }

  getDaysOverdue(invoice: CustomerInvoice): number {
    if (!this.isOverdue(invoice)) {
      return 0;
    }
    const dueDate = new Date(invoice.due_date);
    const today = new Date();
    const diffTime = today.getTime() - dueDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}

export const customerInvoiceService = new CustomerInvoiceService();
export default customerInvoiceService;
