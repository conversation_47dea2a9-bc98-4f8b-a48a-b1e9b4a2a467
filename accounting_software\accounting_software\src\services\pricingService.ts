// src/services/pricingService.ts
import api from './api';

const API_BASE = '/pricing';

export interface PriceList {
  id: number;
  name: string;
  description?: string;
  currency: string;
  valid_from: string;
  valid_to?: string;
  is_active: boolean;
  is_default: boolean;
}

export interface PriceListItem {
  id: number;
  price_list: number;
  product: number;
  product_sku?: string; // read-only field from serializer
  product_name?: string; // read-only field from serializer
  product_type?: string; // read-only field from serializer
  unit_price: number;
  min_quantity: number;
  discount_percent: number;
  effective_date: string;
  expiry_date?: string;
}

export interface DiscountRule {
  id: number;
  name: string;
  description?: string;
  discount_percent: number;
  min_quantity?: number;
  customer_group?: string;
  product_category?: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
}

export interface PriceCalculationParams {
  product: number;
  customer?: number;
  quantity?: number;
  date?: string;
}

export interface PriceCalculationResult {
  product_id: number;
  product_code: string;
  product_name: string;
  customer_id?: number;
  quantity: number;
  unit_price: number;
  currency: string;
  cost_price?: number;
  margin?: number;
  margin_percent?: number;
}

export interface ProductCost {
  id: number;
  code: string;
  name: string;
  type: string;
  cost_method: 'standard' | 'average' | 'fifo';
  standard_cost: number;
  average_cost: number;
  last_cost: number;
  category_name?: string;
}

export interface CostAnalysis {
  total_products: number;
  avg_standard_cost: number;
  avg_last_cost: number;
  cost_methods: {
    standard: number;
    average: number;
    fifo: number;
  };
}

export const pricingService = {
  // Price Lists
  getPriceLists: (params?: any) => api.get<PriceList[]>(`${API_BASE}/price-lists/`, { params }),
  getPriceList: (id: number) => api.get<PriceList>(`${API_BASE}/price-lists/${id}/`),
  createPriceList: (data: Partial<PriceList>) => api.post<PriceList>(`${API_BASE}/price-lists/`, data),
  updatePriceList: (id: number, data: Partial<PriceList>) => api.put<PriceList>(`${API_BASE}/price-lists/${id}/`, data),
  deletePriceList: (id: number) => api.delete(`${API_BASE}/price-lists/${id}/`),
  
  // Price List Items
  getPriceListItems: (params?: any) => api.get<PriceListItem[]>(`${API_BASE}/price-list-items/`, { params }),
  createPriceListItem: (data: Partial<PriceListItem>) => api.post<PriceListItem>(`${API_BASE}/price-list-items/`, data),
  updatePriceListItem: (id: number, data: Partial<PriceListItem>) => api.put<PriceListItem>(`${API_BASE}/price-list-items/${id}/`, data),
  deletePriceListItem: (id: number) => api.delete(`${API_BASE}/price-list-items/${id}/`),
  
  // Discount Rules
  getDiscountRules: (params?: any) => api.get<DiscountRule[]>(`${API_BASE}/discount-rules/`, { params }),
  createDiscountRule: (data: Partial<DiscountRule>) => api.post<DiscountRule>(`${API_BASE}/discount-rules/`, data),
  updateDiscountRule: (id: number, data: Partial<DiscountRule>) => api.put<DiscountRule>(`${API_BASE}/discount-rules/${id}/`, data),
  deleteDiscountRule: (id: number) => api.delete(`${API_BASE}/discount-rules/${id}/`),

  // Pricing Calculations
  calculatePrice: (params: PriceCalculationParams) => api.get<PriceCalculationResult>(`${API_BASE}/pricing/get_price/`, { params }),

  // Default Price List
  getDefaultPriceList: () => api.get<PriceList>(`${API_BASE}/price-lists/default/`),

  // Cost Analysis
  getCostAnalysis: (params?: any) => api.get(`${API_BASE}/cost-analysis/`, { params }),
  getMarginAnalysis: (params?: any) => api.get(`${API_BASE}/margin-analysis/`, { params }),

  // Product Cost Management
  getProductCosts: () => api.get('/pricing/product-costs/'),
  updateProductCost: (productId: number, data: Partial<ProductCost>) => api.patch(`/pricing/product-costs/${productId}/update_cost/`, data),
  getProductCostAnalysis: () => api.get('/pricing/product-costs/cost_analysis/'),
};
