// src/services/productService.ts
import api from './api';
import { pricingService } from './pricingService';

export interface UnifiedProduct {
  id: number;
  name: string;
  code?: string;
  sku?: string;
  description?: string;
  type?: string;
  product_type?: string;
  unit_price?: number;
  standard_cost?: number;
  is_active?: boolean;
  status?: string;
  category?: any;
  category_name?: string;
}

class ProductService {
  /**
   * Get all available products from multiple sources
   * This method tries different endpoints and normalizes the data
   */
  async getAllProducts(): Promise<UnifiedProduct[]> {
    const products: UnifiedProduct[] = [];

    try {
      // First, try to create some mock data if no backend is available
      const mockProducts: UnifiedProduct[] = [
        {
          id: 1,
          name: 'Test Product 1',
          code: 'TEST001',
          sku: 'TEST001',
          description: 'Test product for pricing',
          type: 'product',
          unit_price: 100.00,
          is_active: true,
          status: 'active'
        },
        {
          id: 2,
          name: 'Test Product 2',
          code: 'TEST002',
          sku: 'TEST002',
          description: 'Another test product',
          type: 'product',
          unit_price: 200.00,
          is_active: true,
          status: 'active'
        },
        {
          id: 3,
          name: 'Test Service 1',
          code: 'SVC001',
          sku: 'SVC001',
          description: 'Test service for pricing',
          type: 'service',
          unit_price: 150.00,
          is_active: true,
          status: 'active'
        }
      ];

      // Try to get products from different endpoints
      const endpoints = [
        { name: 'pricing', fetcher: () => pricingService.getProductCosts() },
        { name: 'sales', fetcher: () => api.get('/sales/products/') },
        { name: 'gl', fetcher: () => api.get('/gl/products/') },
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying to fetch products from ${endpoint.name} endpoint...`);
          const response = await endpoint.fetcher();

          console.log(`Response from ${endpoint.name}:`, response);

          // Handle Django REST framework pagination
          const data = (response.data as any).results || response.data;

          console.log(`Data from ${endpoint.name}:`, data);
          console.log(`Is array: ${Array.isArray(data)}, Length: ${Array.isArray(data) ? data.length : 'N/A'}`);

          if (Array.isArray(data)) {
            console.log(`Found ${data.length} products from ${endpoint.name} endpoint`);

            if (data.length > 0) {
              console.log(`Sample product from ${endpoint.name}:`, data[0]);

              // Normalize the product data
              const normalizedProducts = data.map(this.normalizeProduct);
              console.log(`Normalized products from ${endpoint.name}:`, normalizedProducts.slice(0, 2));

              products.push(...normalizedProducts);

              // If we found products, we can break or continue to merge from other sources
              // For now, let's use the first successful source
              if (products.length > 0) {
                console.log(`Breaking after finding ${products.length} products from ${endpoint.name}`);
                break;
              }
            } else {
              console.log(`No products found in ${endpoint.name} endpoint`);
            }
          } else {
            console.log(`Data from ${endpoint.name} is not an array:`, typeof data);
          }
        } catch (error) {
          console.warn(`Failed to fetch from ${endpoint.name} endpoint:`, error);
          continue;
        }
      }

      // If no products found from APIs, use mock data
      if (products.length === 0) {
        console.log('No products found from APIs, using mock data');
        products.push(...mockProducts);
      }

      // Remove duplicates based on ID
      const uniqueProducts = this.removeDuplicates(products);

      console.log(`Total unique products found: ${uniqueProducts.length}`);
      return uniqueProducts;
      
    } catch (error) {
      console.error('Failed to fetch products from all endpoints:', error);
      return [];
    }
  }

  /**
   * Normalize product data from different sources
   */
  private normalizeProduct(product: any): UnifiedProduct {
    return {
      id: product.id,
      name: product.name || product.product_name,
      code: product.code || product.product_code,
      sku: product.sku,
      description: product.description,
      type: product.type || product.product_type,
      product_type: product.product_type || product.type,
      unit_price: product.unit_price || product.price,
      standard_cost: product.standard_cost,
      is_active: product.is_active !== undefined ? product.is_active : product.status === 'active',
      status: product.status,
      category: product.category,
      category_name: product.category_name,
    };
  }

  /**
   * Remove duplicate products based on ID
   */
  private removeDuplicates(products: UnifiedProduct[]): UnifiedProduct[] {
    const seen = new Set<number>();
    return products.filter(product => {
      if (seen.has(product.id)) {
        return false;
      }
      seen.add(product.id);
      return true;
    });
  }

  /**
   * Get active products only
   */
  async getActiveProducts(): Promise<UnifiedProduct[]> {
    const allProducts = await this.getAllProducts();
    return allProducts.filter(product => 
      product.is_active === true || product.status === 'active'
    );
  }

  /**
   * Search products by name or code
   */
  async searchProducts(query: string): Promise<UnifiedProduct[]> {
    const allProducts = await this.getAllProducts();
    const lowerQuery = query.toLowerCase();
    
    return allProducts.filter(product =>
      product.name.toLowerCase().includes(lowerQuery) ||
      (product.code && product.code.toLowerCase().includes(lowerQuery)) ||
      (product.sku && product.sku.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * Get products by type
   */
  async getProductsByType(type: string): Promise<UnifiedProduct[]> {
    const allProducts = await this.getAllProducts();
    return allProducts.filter(product =>
      product.type === type || product.product_type === type
    );
  }
}

// Export singleton instance
export const productService = new ProductService();
export default productService;
