import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  IconButton,
  Alert,
  Snackbar,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Receipt as ReceiptIcon,
  CheckCircle as CheckCircleIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';
import { vendorBillService } from '../../../services/vendor-bill.service';

interface ReturnNote {
  return_note_id: number;
  return_number: string;
  vendor_id: number;
  vendor_name: string;
  return_date: string;
  status: string;
  return_reason: string;
  total_value: number;
  items_count: number;
  original_grn_number: string;
  items: Array<{
    return_item_id: number;
    product: number;
    product_name: string;
    quantity_returned: number;
    unit_cost: number;
    return_value: number;
    return_reason: string;
  }>;
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const CreateVendorCreditFromReturnPage: React.FC = () => {
  const navigate = useNavigate();
  const [availableReturns, setAvailableReturns] = useState<ReturnNote[]>([]);
  const [selectedReturn, setSelectedReturn] = useState<ReturnNote | null>(null);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Load available return notes
  const loadAvailableReturns = async () => {
    try {
      setLoading(true);
      const returns = await vendorBillService.getBillableReturnNotes();
      console.log('📋 Loaded billable return notes:', returns);
      setAvailableReturns(returns);
    } catch (error) {
      console.error('Failed to load return notes:', error);
      setSnackbar({
        open: true,
        message: error instanceof Error ? error.message : 'Failed to load return notes',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle creating vendor credit from selected return note
  const handleCreateFromReturn = async () => {
    if (!selectedReturn) return;

    setLoading(true);
    try {
      const billData = {
        bill_date: dayjs().format('YYYY-MM-DD'),
        due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
        notes: `Credit created from Goods Return Note ${selectedReturn.return_number}`,
      };

      await vendorBillService.createVendorCreditFromReturnNote(selectedReturn.return_note_id, billData);

      setSnackbar({
        open: true,
        message: `Vendor credit bill created successfully from Return Note ${selectedReturn.return_number}`,
        severity: 'success',
      });

      // Navigate to vendor bills page after a short delay
      setTimeout(() => {
        navigate('/dashboard/purchases/vendor-bills');
      }, 2000);

    } catch (error) {
      console.error('Failed to create vendor credit from return note:', error);
      setSnackbar({
        open: true,
        message: error instanceof Error ? error.message : 'Failed to create vendor credit from return note',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (selectedReturn) {
      setSelectedReturn(null);
      navigate('/dashboard/purchases/vendor-bills/create-credit-from-return', { replace: true });
    } else {
      navigate('/dashboard/purchases/vendor-bills');
    }
  };

  // Handle snackbar close
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Load data on component mount
  useEffect(() => {
    loadAvailableReturns();
  }, []);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'RETURNED': return 'primary';
      case 'POSTED': return 'secondary';
      default: return 'default';
    }
  };

  // Show return note selection view
  if (!selectedReturn) {
    return (
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              Select Goods Return Note
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Choose a goods return note to create vendor credit bill from. Only approved/returned goods return notes are shown.
            </Typography>
          </Box>
        </Box>

        {/* Return Note Selection Grid */}
        <Box sx={{ flexGrow: 1 }}>
          <Grid container spacing={3}>
            {availableReturns.length === 0 ? (
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <ReceiptIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                    No Goods Return Notes Available
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    There are no goods return notes available for credit creation. Only approved/returned goods return notes that haven't been credited yet will appear here.
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/dashboard/inventory/grn-returns')}
                    sx={{ borderRadius: '8px' }}
                  >
                    View Goods Return Notes
                  </Button>
                </Box>
              </Grid>
            ) : (
              availableReturns.map((returnNote) => (
                <Grid item xs={12} md={6} lg={4} key={returnNote.return_note_id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3,
                      },
                      border: '1px solid',
                      borderColor: 'divider',
                    }}
                    onClick={() => setSelectedReturn(returnNote)}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                          {returnNote.return_number}
                        </Typography>
                        <Chip 
                          label={returnNote.status} 
                          color={getStatusColor(returnNote.status) as any}
                          size="small"
                        />
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <BusinessIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          {returnNote.vendor_name}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <CalendarIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          {dayjs(returnNote.return_date).format('MMM DD, YYYY')}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <AssignmentIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          {returnNote.items_count} items • ${returnNote.total_value.toFixed(2)}
                        </Typography>
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Reason:</strong> {returnNote.return_reason}
                      </Typography>

                      <Typography variant="body2" color="text.secondary">
                        <strong>Original GRN:</strong> {returnNote.original_grn_number}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    );
  }

  // Show selected return note details and create credit option
  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={handleBack} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            Create Credit from Return Note
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            Review return note details and create vendor credit bill
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Return Note Details */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Return Note Details
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Return Number</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>{selectedReturn.return_number}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Status</Typography>
                  <Chip label={selectedReturn.status} color={getStatusColor(selectedReturn.status) as any} size="small" />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Vendor</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>{selectedReturn.vendor_name}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Return Date</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {dayjs(selectedReturn.return_date).format('MMM DD, YYYY')}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Return Reason</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>{selectedReturn.return_reason}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Original GRN</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>{selectedReturn.original_grn_number}</Typography>
                </Grid>
              </Grid>

              {/* Items Table */}
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Return Items
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell align="right">Qty Returned</TableCell>
                      <TableCell align="right">Unit Cost</TableCell>
                      <TableCell align="right">Return Value</TableCell>
                      <TableCell>Reason</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedReturn.items.map((item) => (
                      <TableRow key={item.return_item_id}>
                        <TableCell>{item.product_name}</TableCell>
                        <TableCell align="right">{item.quantity_returned}</TableCell>
                        <TableCell align="right">${item.unit_cost.toFixed(2)}</TableCell>
                        <TableCell align="right">${item.return_value.toFixed(2)}</TableCell>
                        <TableCell>{item.return_reason}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Total Return Value: ${selectedReturn.total_value.toFixed(2)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Action Card */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Create Vendor Credit
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Click "Create Credit from Return" to automatically create a vendor credit bill with all return details. This will create a credit entry to reduce your payables.
                </Typography>
              </Alert>

              <Button
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CheckCircleIcon />}
                onClick={handleCreateFromReturn}
                disabled={loading}
                fullWidth
                size="large"
                sx={{
                  bgcolor: 'error.main',
                  '&:hover': { bgcolor: 'error.dark' },
                  borderRadius: '8px',
                }}
              >
                {loading ? 'Creating Credit...' : 'Create Credit from Return'}
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateVendorCreditFromReturnPage;
